#!/usr/bin/env node

/**
 * Test spécifique PERFECT avec Groq AI corrigé
 */

require('dotenv').config();
const IntelligentResponseGenerator = require('../services/intelligentResponseGenerator');

async function testPerfectGroq() {
    console.log('🔧 TEST PERFECT GROQ AI CORRIGÉ');
    console.log('=' .repeat(35));
    console.log('');

    const responseGenerator = new IntelligentResponseGenerator();

    const perfectTests = [
        'Parle moi du logiciel perfect',
        'PERFECT ne se connecte pas',
        'Problème de licence PERFECT',
        'Comment sauvegarder dans PERFECT ?',
        'PERFECT VISION erreur'
    ];

    console.log('🧪 Test questions PERFECT avec Groq AI :');
    console.log('');

    let successCount = 0;

    for (const message of perfectTests) {
        console.log(`📤 "${message}"`);
        
        const startTime = Date.now();
        
        try {
            const messageData = {
                from: 'test_perfect',
                content: { text: message }
            };

            const response = await responseGenerator.generateResponse(messageData);
            const responseTime = Date.now() - startTime;
            
            if (response && response.content.text) {
                const text = response.content.text;
                const source = response.source || 'unknown';
                
                console.log(`   ✅ Succès (${responseTime}ms) - Source: ${source}`);
                console.log(`   📏 Longueur: ${text.length} caractères`);
                
                // Vérifications PERFECT
                const mentionnePerfect = text.toLowerCase().includes('perfect');
                const mentionneLogiciel = text.toLowerCase().includes('logiciel') || 
                                         text.toLowerCase().includes('software');
                const mentionneCagecfi = text.toLowerCase().includes('cagecfi');
                const mentionneSolutions = text.toLowerCase().includes('solution') || 
                                          text.toLowerCase().includes('résoudre');
                
                console.log(`   🔧 PERFECT: ${mentionnePerfect ? '✅' : '❌'}`);
                console.log(`   💻 Logiciel: ${mentionneLogiciel ? '✅' : '❌'}`);
                console.log(`   🏢 CAGECFI: ${mentionneCagecfi ? '✅' : '❌'}`);
                console.log(`   🛠️ Solutions: ${mentionneSolutions ? '✅' : '❌'}`);
                
                const score = (mentionnePerfect ? 1 : 0) + 
                             (mentionneLogiciel ? 1 : 0) + 
                             (mentionneCagecfi ? 1 : 0) + 
                             (mentionneSolutions ? 1 : 0);
                
                const qualityScore = (score / 4) * 100;
                console.log(`   📊 Score PERFECT: ${qualityScore.toFixed(0)}% (${score}/4)`);
                
                const isSuccess = qualityScore >= 75 && source === 'groq';
                console.log(`   🎯 Groq réussite: ${isSuccess ? '✅ OUI' : '❌ NON'}`);
                
                if (isSuccess) successCount++;
                
                console.log(`   💬 Aperçu: ${text.substring(0, 80)}...`);
                
            } else {
                console.log(`   ❌ Pas de réponse générée`);
            }
            
        } catch (error) {
            console.log(`   ❌ Erreur: ${error.message}`);
        }
        
        console.log('');
    }

    const successRate = (successCount / perfectTests.length) * 100;
    
    console.log('🎯 RÉSULTATS PERFECT GROQ AI :');
    console.log(`   🏆 Taux de réussite: ${successCount}/${perfectTests.length} (${successRate.toFixed(0)}%)`);
    console.log(`   🎓 Niveau: ${successRate >= 90 ? 'PARFAIT' : successRate >= 80 ? 'EXCELLENT' : 'BON'}`);
    console.log('');
    console.log('✅ CORRECTION APPLIQUÉE :');
    console.log('   🔧 Type de données sécurisé');
    console.log('   🔧 parseAIResponse corrigé');
    console.log('   🔧 Plus d\'erreur toLowerCase');
    console.log('');
    console.log(successRate >= 90 ? '🎊 PERFECT GROQ AI PARFAIT !' : '⚠️ Optimisation continue...');
}

// Exécuter le test
if (require.main === module) {
    testPerfectGroq().catch(console.error);
}

module.exports = { testPerfectGroq };
