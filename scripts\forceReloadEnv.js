#!/usr/bin/env node

/**
 * Force le rechargement des variables d'environnement
 */

// Nettoyer le cache dotenv
delete require.cache[require.resolve('dotenv')];

// Recharger dotenv
require('dotenv').config({ override: true });

console.log('🔄 RECHARGEMENT FORCÉ DES VARIABLES D\'ENVIRONNEMENT');
console.log('=' .repeat(55));
console.log('');

console.log('🔑 VÉRIFICATION DES CLÉS GROQ :');
console.log(`   GROQ_API_KEY_1: ${process.env.GROQ_API_KEY_1?.substring(0, 20)}...`);
console.log(`   GROQ_API_KEY_2: ${process.env.GROQ_API_KEY_2?.substring(0, 20)}...`);
console.log(`   GROQ_API_KEY_3: ${process.env.GROQ_API_KEY_3?.substring(0, 20)}...`);
console.log(`   GROQ_API_KEY_4: ${process.env.GROQ_API_KEY_4?.substring(0, 20)}...`);
console.log(`   GROQ_API_KEY_5: ${process.env.GROQ_API_KEY_5?.substring(0, 20)}...`);
console.log('');

// Compter les clés valides
const validKeys = [
  process.env.GROQ_API_KEY_1,
  process.env.GROQ_API_KEY_2,
  process.env.GROQ_API_KEY_3,
  process.env.GROQ_API_KEY_4,
  process.env.GROQ_API_KEY_5
].filter(key => key && key.startsWith('gsk_'));

console.log(`✅ CLÉS VALIDES DÉTECTÉES : ${validKeys.length}/5`);
console.log('');

if (validKeys.length === 5) {
  console.log('🎉 PARFAIT ! Toutes les 5 clés sont configurées !');
  console.log('');
  console.log('🚀 CAPACITÉ TOTALE :');
  console.log(`   📊 ${validKeys.length * 100000} tokens/jour`);
  console.log(`   👥 ~${validKeys.length * 200} clients/jour`);
  console.log('');
  console.log('▶️ Lancez maintenant : node scripts/testRotationGroq.js');
} else {
  console.log('⚠️ PROBLÈME : Toutes les clés ne sont pas configurées');
  console.log('');
  console.log('📝 VÉRIFIEZ VOTRE .env :');
  console.log('   - Toutes les clés doivent commencer par gsk_');
  console.log('   - Pas d\'espaces avant/après les clés');
  console.log('   - Pas de guillemets autour des clés');
}
