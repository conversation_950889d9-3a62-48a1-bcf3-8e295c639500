{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:54:15"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:54:35"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:54:48"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:55:01"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:55:14"}
{"level":"error","message":"Erreur lors de la génération de réponse: this.generateCommercialCreditResponse is not a function","service":"focep-chatbot","stack":"TypeError: this.generateCommercialCreditResponse is not a function\n    at ResponseGenerator.generateCommercialCreditResponse [as generateResponse] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\responseGenerator.js:114:23)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\tests\\unit\\responseGenerator.test.js:111:24)","timestamp":"2025-07-27 08:55:14"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:55:24"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:55:34"}
{"level":"error","message":"Erreur lors de la génération de réponse: this.generateMicroinsuranceResponse is not a function","service":"focep-chatbot","stack":"TypeError: this.generateMicroinsuranceResponse is not a function\n    at ResponseGenerator.generateMicroinsuranceResponse [as generateResponse] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\responseGenerator.js:117:23)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\tests\\unit\\responseGenerator.test.js:157:24)","timestamp":"2025-07-27 08:55:34"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:55:44"}
{"level":"error","message":"Erreur lors de la génération de réponse: this.generateMobileMoneyResponse is not a function","service":"focep-chatbot","stack":"TypeError: this.generateMobileMoneyResponse is not a function\n    at ResponseGenerator.generateMobileMoneyResponse [as generateResponse] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\responseGenerator.js:120:23)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\tests\\unit\\responseGenerator.test.js:178:24)","timestamp":"2025-07-27 08:55:44"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:55:54"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:56:05"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:56:15"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:56:25"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:56:35"}
{"level":"error","message":"Erreur lors de la génération de réponse prêt: Operation `products.find()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `products.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:56:45"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:56:55"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:57:08"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:57:22"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:57:35"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:57:46"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:57:57"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:58:07"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:58:17"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:58:27"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:58:37"}
{"level":"error","message":"Erreur lors de la génération de réponse: Cannot read properties of undefined (reading 'greeting')","service":"focep-chatbot","stack":"TypeError: Cannot read properties of undefined (reading 'greeting')\n    at ResponseGenerator.greeting [as generateGreetingResponse] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\responseGenerator.js:240:52)\n    at ResponseGenerator.generateGreetingResponse [as generateResponse] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\responseGenerator.js:93:23)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\tests\\unit\\responseGenerator.test.js:455:24)","timestamp":"2025-07-27 08:58:37"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:58:47"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 08:58:57"}
{"code":"ERR_BAD_REQUEST","component":"nlp","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":"{\"model\":\"deepseek-chat\",\"messages\":[{\"role\":\"system\",\"content\":\"Tu es l'assistant virtuel intelligent de FOCEP SA (Fonds Camerounais d'Épargne et de Crédit pour la Production), une institution de microfinance camerounaise de référence.\\n\\nINFORMATIONS IMPORTANTES SUR FOCEP SA :\\n- Fondée en 2007, 18 ans d'expérience\\n- 250 employés, 15 agences\\n- Plus de 50 000 clients satisfaits\\n- Capital : 1.5 milliard FCFA\\n- Classement : Parmi les 10 meilleurs EMF du Cameroun\\n- Objectif 2027 : Être dans le TOP 5 des meilleurs EMF au Cameroun\\n- Leadership : PCA Eugène Nzouadjeu Yota, DG Bertin Tonleu\\n- Partenaire stratégique : Finafrica depuis 2019\\n\\nCONTACT :\\n- Téléphone : +237 695 021 141\\n- Email : <EMAIL>\\n- Site web : www.focep.cm\\n- Siège : Yaoundé, Cameroun\\n\\nINSTRUCTIONS DE COMPORTEMENT :\\n1. Sois toujours poli, professionnel et empathique\\n2. Utilise un langage chaleureux et personnalisé\\n3. Fournis des informations précises et factuelles uniquement\\n4. Si tu ne connais pas une information, oriente vers un agent humain\\n5. Utilise des émojis appropriés pour rendre les réponses attrayantes\\n6. Adapte ton niveau de langage au contexte bancaire/financier\\n7. Reste dans le contexte de FOCEP SA - ne réponds pas aux questions hors sujet\\n8. Encourage toujours l'engagement client et la satisfaction\\n\\nPRODUITS ET SERVICES DISPONIBLES :\\n- Compte d'épargne: Compte à vue qui permet à son titulaire physique ou morale (associations, GIC, GIE) de sécuriser son...\\n- Compte courant et compte de dépôt simple: Comptes destinés aux commerçants et particuliers sollicitant bénéficier des services bancaires pour ...\\n- Bons de caisse: C'est l'opportunité de placement de fonds offerte par FOCEP à sa clientèle. Ce produit permet aux cl...\\n- Crédits aux jeunes entrepreneurs: Les crédits aux jeunes entrepreneurs pour le financement des activités agro-pastorales et des chaîne...\\n- Crédits aux commerçants et assimilés: Crédits pour le renforcement du fonds de roulement des commerçants et assimilés...\\n- Crédits aux fonctionnaires et employés d'entreprises: Crédits pour le financement des besoins des fonctionnaires et employés d'entreprises domiciliées et ...\\n- Crédit scolaire: Crédit spécialement conçu pour financer les frais de scolarité et fournitures scolaires...\\n- Crédit agricole saisonnier: Financement des activités agricoles selon les saisons de culture...\\n\\nAGENCES PRINCIPALES :\\n- FOCEP Yaoundé Centre (Yaoundé): +237 222 23 14 83\\n- FOCEP Mokolo (Yaoundé): +237 222 23 14 84\\n- FOCEP Douala Ndokoti (Douala): +237 233 42 15 67\\n- FOCEP Douala Akwa (Douala): +237 233 42 15 68\\n- FOCEP Douala Mboppi (Douala): +237 233 42 15 69\\n\\nAnalyse ce message et retourne UNIQUEMENT un objet JSON avec cette structure exacte :\\n{\\n  \\\"intent\\\": \\\"intention_detectee\\\",\\n  \\\"confidence\\\": 0.95,\\n  \\\"language\\\": \\\"fr\\\",\\n  \\\"entities\\\": [{\\\"entity\\\": \\\"type\\\", \\\"value\\\": \\\"valeur\\\", \\\"confidence\\\": 0.9}],\\n  \\\"sentiment\\\": \\\"positive|negative|neutral\\\",\\n  \\\"sentimentScore\\\": 0.5,\\n  \\\"context\\\": \\\"contexte_conversationnel\\\",\\n  \\\"needsHumanAgent\\\": false\\n}\"},{\"role\":\"user\",\"content\":\"Bonjour, je voudrais des informations sur vos crédits\"}],\"max_tokens\":500,\"temperature\":0.3,\"stream\":false}","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3291","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"https://api.deepseek.com/v1/chat/completions","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"level":"error","message":"Erreur lors de l'analyse DeepSeek: Request failed with status code 402","name":"AxiosError","request":{"_closed":true,"_contentLength":3291,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer sk-faed2ca2fb944a91a52266d4db96110c\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 3291\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: api.deepseek.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":false,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://api.deepseek.com/v1/chat/completions","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3291","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"hostname":"api.deepseek.com","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/v1/chat/completions","pathname":"/v1/chat/completions","port":"","protocol":"https:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":3291,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"api.deepseek.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/v1/chat/completions","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Sun, 27 Jul 2025 15:20:25 GMT","Content-Type","application/json","Content-Length","111","Connection","keep-alive","vary","origin, access-control-request-method, access-control-request-headers","access-control-allow-credentials","true","x-ds-trace-id","9345178942b89f306bc5d94999636d21","Strict-Transport-Security","max-age=31536000; includeSubDomains; preload","X-Content-Type-Options","nosniff","cf-cache-status","DYNAMIC","Set-Cookie","__cf_bm=bThBshqNewPZdyFSYQpNQQ2.PM6XFFVgIHLer533lBk-1753629625-*******-Oy_yhJCfIXdF1kUkZXXwmZNci_votpdXl9bmW3kyK.tckvWcBWnPfmM5HNDzOwUWrIk18MwoGgVrHAIo_yCmrl2d7WLov0NyerDRfmg.hss; path=/; expires=Sun, 27-Jul-25 15:50:25 GMT; domain=.deepseek.com; HttpOnly; Secure; SameSite=None","Server","cloudflare","CF-RAY","965d1ee218bdd07a-LHR"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://api.deepseek.com/v1/chat/completions","socket":null,"statusCode":402,"statusMessage":"Payment Required","upgrade":false,"url":""},"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"response":{"config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":"{\"model\":\"deepseek-chat\",\"messages\":[{\"role\":\"system\",\"content\":\"Tu es l'assistant virtuel intelligent de FOCEP SA (Fonds Camerounais d'Épargne et de Crédit pour la Production), une institution de microfinance camerounaise de référence.\\n\\nINFORMATIONS IMPORTANTES SUR FOCEP SA :\\n- Fondée en 2007, 18 ans d'expérience\\n- 250 employés, 15 agences\\n- Plus de 50 000 clients satisfaits\\n- Capital : 1.5 milliard FCFA\\n- Classement : Parmi les 10 meilleurs EMF du Cameroun\\n- Objectif 2027 : Être dans le TOP 5 des meilleurs EMF au Cameroun\\n- Leadership : PCA Eugène Nzouadjeu Yota, DG Bertin Tonleu\\n- Partenaire stratégique : Finafrica depuis 2019\\n\\nCONTACT :\\n- Téléphone : +237 695 021 141\\n- Email : <EMAIL>\\n- Site web : www.focep.cm\\n- Siège : Yaoundé, Cameroun\\n\\nINSTRUCTIONS DE COMPORTEMENT :\\n1. Sois toujours poli, professionnel et empathique\\n2. Utilise un langage chaleureux et personnalisé\\n3. Fournis des informations précises et factuelles uniquement\\n4. Si tu ne connais pas une information, oriente vers un agent humain\\n5. Utilise des émojis appropriés pour rendre les réponses attrayantes\\n6. Adapte ton niveau de langage au contexte bancaire/financier\\n7. Reste dans le contexte de FOCEP SA - ne réponds pas aux questions hors sujet\\n8. Encourage toujours l'engagement client et la satisfaction\\n\\nPRODUITS ET SERVICES DISPONIBLES :\\n- Compte d'épargne: Compte à vue qui permet à son titulaire physique ou morale (associations, GIC, GIE) de sécuriser son...\\n- Compte courant et compte de dépôt simple: Comptes destinés aux commerçants et particuliers sollicitant bénéficier des services bancaires pour ...\\n- Bons de caisse: C'est l'opportunité de placement de fonds offerte par FOCEP à sa clientèle. Ce produit permet aux cl...\\n- Crédits aux jeunes entrepreneurs: Les crédits aux jeunes entrepreneurs pour le financement des activités agro-pastorales et des chaîne...\\n- Crédits aux commerçants et assimilés: Crédits pour le renforcement du fonds de roulement des commerçants et assimilés...\\n- Crédits aux fonctionnaires et employés d'entreprises: Crédits pour le financement des besoins des fonctionnaires et employés d'entreprises domiciliées et ...\\n- Crédit scolaire: Crédit spécialement conçu pour financer les frais de scolarité et fournitures scolaires...\\n- Crédit agricole saisonnier: Financement des activités agricoles selon les saisons de culture...\\n\\nAGENCES PRINCIPALES :\\n- FOCEP Yaoundé Centre (Yaoundé): +237 222 23 14 83\\n- FOCEP Mokolo (Yaoundé): +237 222 23 14 84\\n- FOCEP Douala Ndokoti (Douala): +237 233 42 15 67\\n- FOCEP Douala Akwa (Douala): +237 233 42 15 68\\n- FOCEP Douala Mboppi (Douala): +237 233 42 15 69\\n\\nAnalyse ce message et retourne UNIQUEMENT un objet JSON avec cette structure exacte :\\n{\\n  \\\"intent\\\": \\\"intention_detectee\\\",\\n  \\\"confidence\\\": 0.95,\\n  \\\"language\\\": \\\"fr\\\",\\n  \\\"entities\\\": [{\\\"entity\\\": \\\"type\\\", \\\"value\\\": \\\"valeur\\\", \\\"confidence\\\": 0.9}],\\n  \\\"sentiment\\\": \\\"positive|negative|neutral\\\",\\n  \\\"sentimentScore\\\": 0.5,\\n  \\\"context\\\": \\\"contexte_conversationnel\\\",\\n  \\\"needsHumanAgent\\\": false\\n}\"},{\"role\":\"user\",\"content\":\"Bonjour, je voudrais des informations sur vos crédits\"}],\"max_tokens\":500,\"temperature\":0.3,\"stream\":false}","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3291","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"https://api.deepseek.com/v1/chat/completions","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"data":{"error":{"code":"invalid_request_error","message":"Insufficient Balance","param":null,"type":"unknown_error"}},"headers":{"access-control-allow-credentials":"true","cf-cache-status":"DYNAMIC","cf-ray":"965d1ee218bdd07a-LHR","connection":"keep-alive","content-length":"111","content-type":"application/json","date":"Sun, 27 Jul 2025 15:20:25 GMT","server":"cloudflare","set-cookie":"__cf_bm=bThBshqNewPZdyFSYQpNQQ2.PM6XFFVgIHLer533lBk-1753629625-*******-Oy_yhJCfIXdF1kUkZXXwmZNci_votpdXl9bmW3kyK.tckvWcBWnPfmM5HNDzOwUWrIk18MwoGgVrHAIo_yCmrl2d7WLov0NyerDRfmg.hss; path=/; expires=Sun, 27-Jul-25 15:50:25 GMT; domain=.deepseek.com; HttpOnly; Secure; SameSite=None","strict-transport-security":"max-age=31536000; includeSubDomains; preload","vary":"origin, access-control-request-method, access-control-request-headers","x-content-type-options":"nosniff","x-ds-trace-id":"9345178942b89f306bc5d94999636d21"},"request":{"_closed":true,"_contentLength":3291,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer sk-faed2ca2fb944a91a52266d4db96110c\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 3291\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: api.deepseek.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":false,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://api.deepseek.com/v1/chat/completions","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3291","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"hostname":"api.deepseek.com","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/v1/chat/completions","pathname":"/v1/chat/completions","port":"","protocol":"https:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":3291,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"api.deepseek.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/v1/chat/completions","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Sun, 27 Jul 2025 15:20:25 GMT","Content-Type","application/json","Content-Length","111","Connection","keep-alive","vary","origin, access-control-request-method, access-control-request-headers","access-control-allow-credentials","true","x-ds-trace-id","9345178942b89f306bc5d94999636d21","Strict-Transport-Security","max-age=31536000; includeSubDomains; preload","X-Content-Type-Options","nosniff","cf-cache-status","DYNAMIC","Set-Cookie","__cf_bm=bThBshqNewPZdyFSYQpNQQ2.PM6XFFVgIHLer533lBk-1753629625-*******-Oy_yhJCfIXdF1kUkZXXwmZNci_votpdXl9bmW3kyK.tckvWcBWnPfmM5HNDzOwUWrIk18MwoGgVrHAIo_yCmrl2d7WLov0NyerDRfmg.hss; path=/; expires=Sun, 27-Jul-25 15:50:25 GMT; domain=.deepseek.com; HttpOnly; Secure; SameSite=None","Server","cloudflare","CF-RAY","965d1ee218bdd07a-LHR"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://api.deepseek.com/v1/chat/completions","socket":null,"statusCode":402,"statusMessage":"Payment Required","upgrade":false,"url":""},"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"status":402,"statusText":"Payment Required"},"service":"focep-chatbot","stack":"AxiosError: Request failed with status code 402\n    at settle (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DeepSeekService.makeAPICall (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\deepSeekService.js:263:22)\n    at async DeepSeekService.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\deepSeekService.js:184:24)\n    at async C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:87:24\n    at async DeepSeekIntegrationValidator.test (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:289:22)\n    at async DeepSeekIntegrationValidator.validateServices (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:86:7)\n    at async DeepSeekIntegrationValidator.runValidation (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:34:7)","status":402,"timestamp":"2025-07-27 17:20:25"}
{"code":"ERR_BAD_REQUEST","component":"nlp","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":"{\"model\":\"deepseek-chat\",\"messages\":[{\"role\":\"system\",\"content\":\"Tu es l'assistant virtuel intelligent de FOCEP SA (Fonds Camerounais d'Épargne et de Crédit pour la Production), une institution de microfinance camerounaise de référence.\\n\\nINFORMATIONS IMPORTANTES SUR FOCEP SA :\\n- Fondée en 2007, 18 ans d'expérience\\n- 250 employés, 15 agences\\n- Plus de 50 000 clients satisfaits\\n- Capital : 1.5 milliard FCFA\\n- Classement : Parmi les 10 meilleurs EMF du Cameroun\\n- Objectif 2027 : Être dans le TOP 5 des meilleurs EMF au Cameroun\\n- Leadership : PCA Eugène Nzouadjeu Yota, DG Bertin Tonleu\\n- Partenaire stratégique : Finafrica depuis 2019\\n\\nCONTACT :\\n- Téléphone : +237 695 021 141\\n- Email : <EMAIL>\\n- Site web : www.focep.cm\\n- Siège : Yaoundé, Cameroun\\n\\nINSTRUCTIONS DE COMPORTEMENT :\\n1. Sois toujours poli, professionnel et empathique\\n2. Utilise un langage chaleureux et personnalisé\\n3. Fournis des informations précises et factuelles uniquement\\n4. Si tu ne connais pas une information, oriente vers un agent humain\\n5. Utilise des émojis appropriés pour rendre les réponses attrayantes\\n6. Adapte ton niveau de langage au contexte bancaire/financier\\n7. Reste dans le contexte de FOCEP SA - ne réponds pas aux questions hors sujet\\n8. Encourage toujours l'engagement client et la satisfaction\\n\\nPRODUITS ET SERVICES DISPONIBLES :\\n- Compte d'épargne: Compte à vue qui permet à son titulaire physique ou morale (associations, GIC, GIE) de sécuriser son...\\n- Compte courant et compte de dépôt simple: Comptes destinés aux commerçants et particuliers sollicitant bénéficier des services bancaires pour ...\\n- Bons de caisse: C'est l'opportunité de placement de fonds offerte par FOCEP à sa clientèle. Ce produit permet aux cl...\\n- Crédits aux jeunes entrepreneurs: Les crédits aux jeunes entrepreneurs pour le financement des activités agro-pastorales et des chaîne...\\n- Crédits aux commerçants et assimilés: Crédits pour le renforcement du fonds de roulement des commerçants et assimilés...\\n- Crédits aux fonctionnaires et employés d'entreprises: Crédits pour le financement des besoins des fonctionnaires et employés d'entreprises domiciliées et ...\\n- Crédit scolaire: Crédit spécialement conçu pour financer les frais de scolarité et fournitures scolaires...\\n- Crédit agricole saisonnier: Financement des activités agricoles selon les saisons de culture...\\n\\nAGENCES PRINCIPALES :\\n- FOCEP Yaoundé Centre (Yaoundé): +237 222 23 14 83\\n- FOCEP Mokolo (Yaoundé): +237 222 23 14 84\\n- FOCEP Douala Ndokoti (Douala): +237 233 42 15 67\\n- FOCEP Douala Akwa (Douala): +237 233 42 15 68\\n- FOCEP Douala Mboppi (Douala): +237 233 42 15 69\\n\\nCONTEXTE CLIENT PERSONNALISÉ :\\n\\nINSTRUCTIONS SPÉCIALES :\\n- Utilise ce contexte pour personnaliser ta réponse\\n- Fais référence aux interactions précédentes si pertinent\\n- Adapte ton niveau de détail selon le type de client\\n- Si le client a demandé un agent humain, propose de le mettre en relation\\n- Maintiens un ton cohérent avec le niveau de satisfaction détecté\\n- Génère une réponse naturelle, empathique et professionnelle\\n- Utilise des émojis appropriés pour rendre la réponse attrayante\\n- Reste factuel et ne invente jamais d'informations sur FOCEP SA\"},{\"role\":\"user\",\"content\":\"Bonjour\"}],\"max_tokens\":1000,\"temperature\":0.7,\"stream\":false}","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3409","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"https://api.deepseek.com/v1/chat/completions","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"level":"error","message":"Erreur lors de la génération de réponse DeepSeek: Request failed with status code 402","name":"AxiosError","request":{"_closed":true,"_contentLength":3409,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer sk-faed2ca2fb944a91a52266d4db96110c\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 3409\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: api.deepseek.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":false,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://api.deepseek.com/v1/chat/completions","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3409","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"hostname":"api.deepseek.com","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/v1/chat/completions","pathname":"/v1/chat/completions","port":"","protocol":"https:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":3409,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"api.deepseek.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/v1/chat/completions","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Sun, 27 Jul 2025 15:20:26 GMT","Content-Type","application/json","Content-Length","111","Connection","keep-alive","vary","origin, access-control-request-method, access-control-request-headers","access-control-allow-credentials","true","x-ds-trace-id","85a1b8aa6b8fe79a81d7446bb32f60bb","Strict-Transport-Security","max-age=31536000; includeSubDomains; preload","X-Content-Type-Options","nosniff","cf-cache-status","DYNAMIC","Set-Cookie","__cf_bm=1Wj8JUTZ5S9bJl3h7FjTAGDxIWhZpRCQv9YenRPZs0Y-1753629626-*******-vqcM1r._0IaR4R1AOyclt6n3AgI_Ai1XAUh12XI3rY9OXKm_oy2lLL8x2T29WstMjkUQMqLWA3bb4fXDrZcXx7AmeonFhwzBDmoWhss4PPM; path=/; expires=Sun, 27-Jul-25 15:50:26 GMT; domain=.deepseek.com; HttpOnly; Secure; SameSite=None","Server","cloudflare","CF-RAY","965d1ee79b5bd07a-LHR"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://api.deepseek.com/v1/chat/completions","socket":null,"statusCode":402,"statusMessage":"Payment Required","upgrade":false,"url":""},"reusedSocket":true,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"response":{"config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":"{\"model\":\"deepseek-chat\",\"messages\":[{\"role\":\"system\",\"content\":\"Tu es l'assistant virtuel intelligent de FOCEP SA (Fonds Camerounais d'Épargne et de Crédit pour la Production), une institution de microfinance camerounaise de référence.\\n\\nINFORMATIONS IMPORTANTES SUR FOCEP SA :\\n- Fondée en 2007, 18 ans d'expérience\\n- 250 employés, 15 agences\\n- Plus de 50 000 clients satisfaits\\n- Capital : 1.5 milliard FCFA\\n- Classement : Parmi les 10 meilleurs EMF du Cameroun\\n- Objectif 2027 : Être dans le TOP 5 des meilleurs EMF au Cameroun\\n- Leadership : PCA Eugène Nzouadjeu Yota, DG Bertin Tonleu\\n- Partenaire stratégique : Finafrica depuis 2019\\n\\nCONTACT :\\n- Téléphone : +237 695 021 141\\n- Email : <EMAIL>\\n- Site web : www.focep.cm\\n- Siège : Yaoundé, Cameroun\\n\\nINSTRUCTIONS DE COMPORTEMENT :\\n1. Sois toujours poli, professionnel et empathique\\n2. Utilise un langage chaleureux et personnalisé\\n3. Fournis des informations précises et factuelles uniquement\\n4. Si tu ne connais pas une information, oriente vers un agent humain\\n5. Utilise des émojis appropriés pour rendre les réponses attrayantes\\n6. Adapte ton niveau de langage au contexte bancaire/financier\\n7. Reste dans le contexte de FOCEP SA - ne réponds pas aux questions hors sujet\\n8. Encourage toujours l'engagement client et la satisfaction\\n\\nPRODUITS ET SERVICES DISPONIBLES :\\n- Compte d'épargne: Compte à vue qui permet à son titulaire physique ou morale (associations, GIC, GIE) de sécuriser son...\\n- Compte courant et compte de dépôt simple: Comptes destinés aux commerçants et particuliers sollicitant bénéficier des services bancaires pour ...\\n- Bons de caisse: C'est l'opportunité de placement de fonds offerte par FOCEP à sa clientèle. Ce produit permet aux cl...\\n- Crédits aux jeunes entrepreneurs: Les crédits aux jeunes entrepreneurs pour le financement des activités agro-pastorales et des chaîne...\\n- Crédits aux commerçants et assimilés: Crédits pour le renforcement du fonds de roulement des commerçants et assimilés...\\n- Crédits aux fonctionnaires et employés d'entreprises: Crédits pour le financement des besoins des fonctionnaires et employés d'entreprises domiciliées et ...\\n- Crédit scolaire: Crédit spécialement conçu pour financer les frais de scolarité et fournitures scolaires...\\n- Crédit agricole saisonnier: Financement des activités agricoles selon les saisons de culture...\\n\\nAGENCES PRINCIPALES :\\n- FOCEP Yaoundé Centre (Yaoundé): +237 222 23 14 83\\n- FOCEP Mokolo (Yaoundé): +237 222 23 14 84\\n- FOCEP Douala Ndokoti (Douala): +237 233 42 15 67\\n- FOCEP Douala Akwa (Douala): +237 233 42 15 68\\n- FOCEP Douala Mboppi (Douala): +237 233 42 15 69\\n\\nCONTEXTE CLIENT PERSONNALISÉ :\\n\\nINSTRUCTIONS SPÉCIALES :\\n- Utilise ce contexte pour personnaliser ta réponse\\n- Fais référence aux interactions précédentes si pertinent\\n- Adapte ton niveau de détail selon le type de client\\n- Si le client a demandé un agent humain, propose de le mettre en relation\\n- Maintiens un ton cohérent avec le niveau de satisfaction détecté\\n- Génère une réponse naturelle, empathique et professionnelle\\n- Utilise des émojis appropriés pour rendre la réponse attrayante\\n- Reste factuel et ne invente jamais d'informations sur FOCEP SA\"},{\"role\":\"user\",\"content\":\"Bonjour\"}],\"max_tokens\":1000,\"temperature\":0.7,\"stream\":false}","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3409","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"https://api.deepseek.com/v1/chat/completions","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"data":{"error":{"code":"invalid_request_error","message":"Insufficient Balance","param":null,"type":"unknown_error"}},"headers":{"access-control-allow-credentials":"true","cf-cache-status":"DYNAMIC","cf-ray":"965d1ee79b5bd07a-LHR","connection":"keep-alive","content-length":"111","content-type":"application/json","date":"Sun, 27 Jul 2025 15:20:26 GMT","server":"cloudflare","set-cookie":"__cf_bm=1Wj8JUTZ5S9bJl3h7FjTAGDxIWhZpRCQv9YenRPZs0Y-1753629626-*******-vqcM1r._0IaR4R1AOyclt6n3AgI_Ai1XAUh12XI3rY9OXKm_oy2lLL8x2T29WstMjkUQMqLWA3bb4fXDrZcXx7AmeonFhwzBDmoWhss4PPM; path=/; expires=Sun, 27-Jul-25 15:50:26 GMT; domain=.deepseek.com; HttpOnly; Secure; SameSite=None","strict-transport-security":"max-age=31536000; includeSubDomains; preload","vary":"origin, access-control-request-method, access-control-request-headers","x-content-type-options":"nosniff","x-ds-trace-id":"85a1b8aa6b8fe79a81d7446bb32f60bb"},"request":{"_closed":true,"_contentLength":3409,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer sk-faed2ca2fb944a91a52266d4db96110c\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 3409\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: api.deepseek.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":false,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://api.deepseek.com/v1/chat/completions","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3409","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"hostname":"api.deepseek.com","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/v1/chat/completions","pathname":"/v1/chat/completions","port":"","protocol":"https:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":3409,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"api.deepseek.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/v1/chat/completions","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Sun, 27 Jul 2025 15:20:26 GMT","Content-Type","application/json","Content-Length","111","Connection","keep-alive","vary","origin, access-control-request-method, access-control-request-headers","access-control-allow-credentials","true","x-ds-trace-id","85a1b8aa6b8fe79a81d7446bb32f60bb","Strict-Transport-Security","max-age=31536000; includeSubDomains; preload","X-Content-Type-Options","nosniff","cf-cache-status","DYNAMIC","Set-Cookie","__cf_bm=1Wj8JUTZ5S9bJl3h7FjTAGDxIWhZpRCQv9YenRPZs0Y-1753629626-*******-vqcM1r._0IaR4R1AOyclt6n3AgI_Ai1XAUh12XI3rY9OXKm_oy2lLL8x2T29WstMjkUQMqLWA3bb4fXDrZcXx7AmeonFhwzBDmoWhss4PPM; path=/; expires=Sun, 27-Jul-25 15:50:26 GMT; domain=.deepseek.com; HttpOnly; Secure; SameSite=None","Server","cloudflare","CF-RAY","965d1ee79b5bd07a-LHR"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://api.deepseek.com/v1/chat/completions","socket":null,"statusCode":402,"statusMessage":"Payment Required","upgrade":false,"url":""},"reusedSocket":true,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"status":402,"statusText":"Payment Required"},"service":"focep-chatbot","stack":"AxiosError: Request failed with status code 402\n    at settle (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DeepSeekService.makeAPICall (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\deepSeekService.js:263:22)\n    at async DeepSeekService.generateContextualResponse (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\deepSeekService.js:321:24)\n    at async C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:101:24\n    at async DeepSeekIntegrationValidator.test (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:289:22)\n    at async DeepSeekIntegrationValidator.validateServices (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:100:7)\n    at async DeepSeekIntegrationValidator.runValidation (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:34:7)","status":402,"timestamp":"2025-07-27 17:20:25"}
{"context":{"language":"fr"},"level":"error","message":"Réponse d'urgence activée","service":"focep-chatbot","timestamp":"2025-07-27 17:20:25"}
{"code":"ERR_BAD_REQUEST","component":"nlp","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":"{\"model\":\"deepseek-chat\",\"messages\":[{\"role\":\"system\",\"content\":\"Tu es l'assistant virtuel intelligent de FOCEP SA (Fonds Camerounais d'Épargne et de Crédit pour la Production), une institution de microfinance camerounaise de référence.\\n\\nINFORMATIONS IMPORTANTES SUR FOCEP SA :\\n- Fondée en 2007, 18 ans d'expérience\\n- 250 employés, 15 agences\\n- Plus de 50 000 clients satisfaits\\n- Capital : 1.5 milliard FCFA\\n- Classement : Parmi les 10 meilleurs EMF du Cameroun\\n- Objectif 2027 : Être dans le TOP 5 des meilleurs EMF au Cameroun\\n- Leadership : PCA Eugène Nzouadjeu Yota, DG Bertin Tonleu\\n- Partenaire stratégique : Finafrica depuis 2019\\n\\nCONTACT :\\n- Téléphone : +237 695 021 141\\n- Email : <EMAIL>\\n- Site web : www.focep.cm\\n- Siège : Yaoundé, Cameroun\\n\\nINSTRUCTIONS DE COMPORTEMENT :\\n1. Sois toujours poli, professionnel et empathique\\n2. Utilise un langage chaleureux et personnalisé\\n3. Fournis des informations précises et factuelles uniquement\\n4. Si tu ne connais pas une information, oriente vers un agent humain\\n5. Utilise des émojis appropriés pour rendre les réponses attrayantes\\n6. Adapte ton niveau de langage au contexte bancaire/financier\\n7. Reste dans le contexte de FOCEP SA - ne réponds pas aux questions hors sujet\\n8. Encourage toujours l'engagement client et la satisfaction\\n\\nPRODUITS ET SERVICES DISPONIBLES :\\n- Compte d'épargne: Compte à vue qui permet à son titulaire physique ou morale (associations, GIC, GIE) de sécuriser son...\\n- Compte courant et compte de dépôt simple: Comptes destinés aux commerçants et particuliers sollicitant bénéficier des services bancaires pour ...\\n- Bons de caisse: C'est l'opportunité de placement de fonds offerte par FOCEP à sa clientèle. Ce produit permet aux cl...\\n- Crédits aux jeunes entrepreneurs: Les crédits aux jeunes entrepreneurs pour le financement des activités agro-pastorales et des chaîne...\\n- Crédits aux commerçants et assimilés: Crédits pour le renforcement du fonds de roulement des commerçants et assimilés...\\n- Crédits aux fonctionnaires et employés d'entreprises: Crédits pour le financement des besoins des fonctionnaires et employés d'entreprises domiciliées et ...\\n- Crédit scolaire: Crédit spécialement conçu pour financer les frais de scolarité et fournitures scolaires...\\n- Crédit agricole saisonnier: Financement des activités agricoles selon les saisons de culture...\\n\\nAGENCES PRINCIPALES :\\n- FOCEP Yaoundé Centre (Yaoundé): +237 222 23 14 83\\n- FOCEP Mokolo (Yaoundé): +237 222 23 14 84\\n- FOCEP Douala Ndokoti (Douala): +237 233 42 15 67\\n- FOCEP Douala Akwa (Douala): +237 233 42 15 68\\n- FOCEP Douala Mboppi (Douala): +237 233 42 15 69\\n\\nAnalyse ce message et retourne UNIQUEMENT un objet JSON avec cette structure exacte :\\n{\\n  \\\"intent\\\": \\\"intention_detectee\\\",\\n  \\\"confidence\\\": 0.95,\\n  \\\"language\\\": \\\"fr\\\",\\n  \\\"entities\\\": [{\\\"entity\\\": \\\"type\\\", \\\"value\\\": \\\"valeur\\\", \\\"confidence\\\": 0.9}],\\n  \\\"sentiment\\\": \\\"positive|negative|neutral\\\",\\n  \\\"sentimentScore\\\": 0.5,\\n  \\\"context\\\": \\\"contexte_conversationnel\\\",\\n  \\\"needsHumanAgent\\\": false\\n}\"},{\"role\":\"user\",\"content\":\"Je voudrais ouvrir un compte épargne\"}],\"max_tokens\":500,\"temperature\":0.3,\"stream\":false}","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3274","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"https://api.deepseek.com/v1/chat/completions","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"level":"error","message":"Erreur lors de l'analyse DeepSeek: Request failed with status code 402","name":"AxiosError","request":{"_closed":true,"_contentLength":3274,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer sk-faed2ca2fb944a91a52266d4db96110c\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 3274\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: api.deepseek.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":false,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://api.deepseek.com/v1/chat/completions","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3274","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"hostname":"api.deepseek.com","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/v1/chat/completions","pathname":"/v1/chat/completions","port":"","protocol":"https:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":3274,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"api.deepseek.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/v1/chat/completions","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Sun, 27 Jul 2025 15:20:26 GMT","Content-Type","application/json","Content-Length","111","Connection","keep-alive","vary","origin, access-control-request-method, access-control-request-headers","access-control-allow-credentials","true","x-ds-trace-id","628d03bbf5b006975540dfdc383cb00c","Strict-Transport-Security","max-age=31536000; includeSubDomains; preload","X-Content-Type-Options","nosniff","cf-cache-status","DYNAMIC","Set-Cookie","__cf_bm=TBRYDP5ZreY_ggUON55RKThLLrsJoMAkkYbP8HcSIIw-1753629626-*******-fRa0eQigOwgGFtQHHyWXB_sOXnZ08XoLC2MRx.hvvWfwYfMnvFYGuG1qd402R05hKmcnGJctrRCpLBhYh0jrGsjsTiyRfgD6s1d05uC8Z3E; path=/; expires=Sun, 27-Jul-25 15:50:26 GMT; domain=.deepseek.com; HttpOnly; Secure; SameSite=None","Server","cloudflare","CF-RAY","965d1eec9e0ed07a-LHR"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://api.deepseek.com/v1/chat/completions","socket":null,"statusCode":402,"statusMessage":"Payment Required","upgrade":false,"url":""},"reusedSocket":true,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"response":{"config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":"{\"model\":\"deepseek-chat\",\"messages\":[{\"role\":\"system\",\"content\":\"Tu es l'assistant virtuel intelligent de FOCEP SA (Fonds Camerounais d'Épargne et de Crédit pour la Production), une institution de microfinance camerounaise de référence.\\n\\nINFORMATIONS IMPORTANTES SUR FOCEP SA :\\n- Fondée en 2007, 18 ans d'expérience\\n- 250 employés, 15 agences\\n- Plus de 50 000 clients satisfaits\\n- Capital : 1.5 milliard FCFA\\n- Classement : Parmi les 10 meilleurs EMF du Cameroun\\n- Objectif 2027 : Être dans le TOP 5 des meilleurs EMF au Cameroun\\n- Leadership : PCA Eugène Nzouadjeu Yota, DG Bertin Tonleu\\n- Partenaire stratégique : Finafrica depuis 2019\\n\\nCONTACT :\\n- Téléphone : +237 695 021 141\\n- Email : <EMAIL>\\n- Site web : www.focep.cm\\n- Siège : Yaoundé, Cameroun\\n\\nINSTRUCTIONS DE COMPORTEMENT :\\n1. Sois toujours poli, professionnel et empathique\\n2. Utilise un langage chaleureux et personnalisé\\n3. Fournis des informations précises et factuelles uniquement\\n4. Si tu ne connais pas une information, oriente vers un agent humain\\n5. Utilise des émojis appropriés pour rendre les réponses attrayantes\\n6. Adapte ton niveau de langage au contexte bancaire/financier\\n7. Reste dans le contexte de FOCEP SA - ne réponds pas aux questions hors sujet\\n8. Encourage toujours l'engagement client et la satisfaction\\n\\nPRODUITS ET SERVICES DISPONIBLES :\\n- Compte d'épargne: Compte à vue qui permet à son titulaire physique ou morale (associations, GIC, GIE) de sécuriser son...\\n- Compte courant et compte de dépôt simple: Comptes destinés aux commerçants et particuliers sollicitant bénéficier des services bancaires pour ...\\n- Bons de caisse: C'est l'opportunité de placement de fonds offerte par FOCEP à sa clientèle. Ce produit permet aux cl...\\n- Crédits aux jeunes entrepreneurs: Les crédits aux jeunes entrepreneurs pour le financement des activités agro-pastorales et des chaîne...\\n- Crédits aux commerçants et assimilés: Crédits pour le renforcement du fonds de roulement des commerçants et assimilés...\\n- Crédits aux fonctionnaires et employés d'entreprises: Crédits pour le financement des besoins des fonctionnaires et employés d'entreprises domiciliées et ...\\n- Crédit scolaire: Crédit spécialement conçu pour financer les frais de scolarité et fournitures scolaires...\\n- Crédit agricole saisonnier: Financement des activités agricoles selon les saisons de culture...\\n\\nAGENCES PRINCIPALES :\\n- FOCEP Yaoundé Centre (Yaoundé): +237 222 23 14 83\\n- FOCEP Mokolo (Yaoundé): +237 222 23 14 84\\n- FOCEP Douala Ndokoti (Douala): +237 233 42 15 67\\n- FOCEP Douala Akwa (Douala): +237 233 42 15 68\\n- FOCEP Douala Mboppi (Douala): +237 233 42 15 69\\n\\nAnalyse ce message et retourne UNIQUEMENT un objet JSON avec cette structure exacte :\\n{\\n  \\\"intent\\\": \\\"intention_detectee\\\",\\n  \\\"confidence\\\": 0.95,\\n  \\\"language\\\": \\\"fr\\\",\\n  \\\"entities\\\": [{\\\"entity\\\": \\\"type\\\", \\\"value\\\": \\\"valeur\\\", \\\"confidence\\\": 0.9}],\\n  \\\"sentiment\\\": \\\"positive|negative|neutral\\\",\\n  \\\"sentimentScore\\\": 0.5,\\n  \\\"context\\\": \\\"contexte_conversationnel\\\",\\n  \\\"needsHumanAgent\\\": false\\n}\"},{\"role\":\"user\",\"content\":\"Je voudrais ouvrir un compte épargne\"}],\"max_tokens\":500,\"temperature\":0.3,\"stream\":false}","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3274","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"https://api.deepseek.com/v1/chat/completions","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"data":{"error":{"code":"invalid_request_error","message":"Insufficient Balance","param":null,"type":"unknown_error"}},"headers":{"access-control-allow-credentials":"true","cf-cache-status":"DYNAMIC","cf-ray":"965d1eec9e0ed07a-LHR","connection":"keep-alive","content-length":"111","content-type":"application/json","date":"Sun, 27 Jul 2025 15:20:26 GMT","server":"cloudflare","set-cookie":"__cf_bm=TBRYDP5ZreY_ggUON55RKThLLrsJoMAkkYbP8HcSIIw-1753629626-*******-fRa0eQigOwgGFtQHHyWXB_sOXnZ08XoLC2MRx.hvvWfwYfMnvFYGuG1qd402R05hKmcnGJctrRCpLBhYh0jrGsjsTiyRfgD6s1d05uC8Z3E; path=/; expires=Sun, 27-Jul-25 15:50:26 GMT; domain=.deepseek.com; HttpOnly; Secure; SameSite=None","strict-transport-security":"max-age=31536000; includeSubDomains; preload","vary":"origin, access-control-request-method, access-control-request-headers","x-content-type-options":"nosniff","x-ds-trace-id":"628d03bbf5b006975540dfdc383cb00c"},"request":{"_closed":true,"_contentLength":3274,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer sk-faed2ca2fb944a91a52266d4db96110c\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 3274\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: api.deepseek.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":false,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://api.deepseek.com/v1/chat/completions","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3274","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"hostname":"api.deepseek.com","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/v1/chat/completions","pathname":"/v1/chat/completions","port":"","protocol":"https:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":3274,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"api.deepseek.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/v1/chat/completions","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Sun, 27 Jul 2025 15:20:26 GMT","Content-Type","application/json","Content-Length","111","Connection","keep-alive","vary","origin, access-control-request-method, access-control-request-headers","access-control-allow-credentials","true","x-ds-trace-id","628d03bbf5b006975540dfdc383cb00c","Strict-Transport-Security","max-age=31536000; includeSubDomains; preload","X-Content-Type-Options","nosniff","cf-cache-status","DYNAMIC","Set-Cookie","__cf_bm=TBRYDP5ZreY_ggUON55RKThLLrsJoMAkkYbP8HcSIIw-1753629626-*******-fRa0eQigOwgGFtQHHyWXB_sOXnZ08XoLC2MRx.hvvWfwYfMnvFYGuG1qd402R05hKmcnGJctrRCpLBhYh0jrGsjsTiyRfgD6s1d05uC8Z3E; path=/; expires=Sun, 27-Jul-25 15:50:26 GMT; domain=.deepseek.com; HttpOnly; Secure; SameSite=None","Server","cloudflare","CF-RAY","965d1eec9e0ed07a-LHR"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://api.deepseek.com/v1/chat/completions","socket":null,"statusCode":402,"statusMessage":"Payment Required","upgrade":false,"url":""},"reusedSocket":true,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"status":402,"statusText":"Payment Required"},"service":"focep-chatbot","stack":"AxiosError: Request failed with status code 402\n    at settle (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DeepSeekService.makeAPICall (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\deepSeekService.js:263:22)\n    at async DeepSeekService.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\deepSeekService.js:184:24)\n    at async NLPService.analyzeWithDeepSeek (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\nlpService.js:511:22)\n    at async NLPService.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\nlpService.js:455:36)\n    at async C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:153:22\n    at async DeepSeekIntegrationValidator.test (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:289:22)\n    at async DeepSeekIntegrationValidator.validateIntegration (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:152:5)\n    at async DeepSeekIntegrationValidator.runValidation (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:35:7)","status":402,"timestamp":"2025-07-27 17:20:26"}
{"context":{"conversationId":"test-integration","language":"fr","text":"Je voudrais ouvrir un compte épargne"},"level":"error","message":"Réponse d'urgence activée","service":"focep-chatbot","timestamp":"2025-07-27 17:20:26"}
{"code":"ERR_BAD_REQUEST","component":"nlp","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":"{\"model\":\"deepseek-chat\",\"messages\":[{\"role\":\"system\",\"content\":\"Tu es l'assistant virtuel intelligent de FOCEP SA (Fonds Camerounais d'Épargne et de Crédit pour la Production), une institution de microfinance camerounaise de référence.\\n\\nINFORMATIONS IMPORTANTES SUR FOCEP SA :\\n- Fondée en 2007, 18 ans d'expérience\\n- 250 employés, 15 agences\\n- Plus de 50 000 clients satisfaits\\n- Capital : 1.5 milliard FCFA\\n- Classement : Parmi les 10 meilleurs EMF du Cameroun\\n- Objectif 2027 : Être dans le TOP 5 des meilleurs EMF au Cameroun\\n- Leadership : PCA Eugène Nzouadjeu Yota, DG Bertin Tonleu\\n- Partenaire stratégique : Finafrica depuis 2019\\n\\nCONTACT :\\n- Téléphone : +237 695 021 141\\n- Email : <EMAIL>\\n- Site web : www.focep.cm\\n- Siège : Yaoundé, Cameroun\\n\\nINSTRUCTIONS DE COMPORTEMENT :\\n1. Sois toujours poli, professionnel et empathique\\n2. Utilise un langage chaleureux et personnalisé\\n3. Fournis des informations précises et factuelles uniquement\\n4. Si tu ne connais pas une information, oriente vers un agent humain\\n5. Utilise des émojis appropriés pour rendre les réponses attrayantes\\n6. Adapte ton niveau de langage au contexte bancaire/financier\\n7. Reste dans le contexte de FOCEP SA - ne réponds pas aux questions hors sujet\\n8. Encourage toujours l'engagement client et la satisfaction\\n\\nPRODUITS ET SERVICES DISPONIBLES :\\n- Compte d'épargne: Compte à vue qui permet à son titulaire physique ou morale (associations, GIC, GIE) de sécuriser son...\\n- Compte courant et compte de dépôt simple: Comptes destinés aux commerçants et particuliers sollicitant bénéficier des services bancaires pour ...\\n- Bons de caisse: C'est l'opportunité de placement de fonds offerte par FOCEP à sa clientèle. Ce produit permet aux cl...\\n- Crédits aux jeunes entrepreneurs: Les crédits aux jeunes entrepreneurs pour le financement des activités agro-pastorales et des chaîne...\\n- Crédits aux commerçants et assimilés: Crédits pour le renforcement du fonds de roulement des commerçants et assimilés...\\n- Crédits aux fonctionnaires et employés d'entreprises: Crédits pour le financement des besoins des fonctionnaires et employés d'entreprises domiciliées et ...\\n- Crédit scolaire: Crédit spécialement conçu pour financer les frais de scolarité et fournitures scolaires...\\n- Crédit agricole saisonnier: Financement des activités agricoles selon les saisons de culture...\\n\\nAGENCES PRINCIPALES :\\n- FOCEP Yaoundé Centre (Yaoundé): +237 222 23 14 83\\n- FOCEP Mokolo (Yaoundé): +237 222 23 14 84\\n- FOCEP Douala Ndokoti (Douala): +237 233 42 15 67\\n- FOCEP Douala Akwa (Douala): +237 233 42 15 68\\n- FOCEP Douala Mboppi (Douala): +237 233 42 15 69\\n\\nCONTEXTE CLIENT PERSONNALISÉ :\\n- Niveau de satisfaction : neutral\\n\\nINSTRUCTIONS SPÉCIALES :\\n- Utilise ce contexte pour personnaliser ta réponse\\n- Fais référence aux interactions précédentes si pertinent\\n- Adapte ton niveau de détail selon le type de client\\n- Si le client a demandé un agent humain, propose de le mettre en relation\\n- Maintiens un ton cohérent avec le niveau de satisfaction détecté\\n- Génère une réponse naturelle, empathique et professionnelle\\n- Utilise des émojis appropriés pour rendre la réponse attrayante\\n- Reste factuel et ne invente jamais d'informations sur FOCEP SA\"},{\"role\":\"user\",\"content\":\"Bonjour\\n\\n[Contexte: Intent détecté: greeting]\"}],\"max_tokens\":1000,\"temperature\":0.7,\"stream\":false}","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3487","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"https://api.deepseek.com/v1/chat/completions","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"level":"error","message":"Erreur lors de la génération de réponse DeepSeek: Request failed with status code 402","name":"AxiosError","request":{"_closed":true,"_contentLength":3487,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer sk-faed2ca2fb944a91a52266d4db96110c\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 3487\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: api.deepseek.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":false,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://api.deepseek.com/v1/chat/completions","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3487","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"hostname":"api.deepseek.com","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/v1/chat/completions","pathname":"/v1/chat/completions","port":"","protocol":"https:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":3487,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"api.deepseek.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/v1/chat/completions","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Sun, 27 Jul 2025 15:20:27 GMT","Content-Type","application/json","Content-Length","111","Connection","keep-alive","vary","origin, access-control-request-method, access-control-request-headers","access-control-allow-credentials","true","x-ds-trace-id","fe0f6da675f6d3210e9fc37f1b104bd7","Strict-Transport-Security","max-age=31536000; includeSubDomains; preload","X-Content-Type-Options","nosniff","cf-cache-status","DYNAMIC","Set-Cookie","__cf_bm=qQ2yuIvrZHTRYqaOf1j0FoHatnHNyJ3m7yir1jl7S3M-1753629627-*******-m6Cl3m1VNRbV1huVxJaXxsb2728DVMJwmkVPZ7sfPoroNSEHacrcG9IczMTKI9JERX1bgObX_qYYgWCO88UvRXX_ifo6k.LtXlMMVmvdxlU; path=/; expires=Sun, 27-Jul-25 15:50:27 GMT; domain=.deepseek.com; HttpOnly; Secure; SameSite=None","Server","cloudflare","CF-RAY","965d1ef1db1ad07a-LHR"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://api.deepseek.com/v1/chat/completions","socket":null,"statusCode":402,"statusMessage":"Payment Required","upgrade":false,"url":""},"reusedSocket":true,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"response":{"config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":"{\"model\":\"deepseek-chat\",\"messages\":[{\"role\":\"system\",\"content\":\"Tu es l'assistant virtuel intelligent de FOCEP SA (Fonds Camerounais d'Épargne et de Crédit pour la Production), une institution de microfinance camerounaise de référence.\\n\\nINFORMATIONS IMPORTANTES SUR FOCEP SA :\\n- Fondée en 2007, 18 ans d'expérience\\n- 250 employés, 15 agences\\n- Plus de 50 000 clients satisfaits\\n- Capital : 1.5 milliard FCFA\\n- Classement : Parmi les 10 meilleurs EMF du Cameroun\\n- Objectif 2027 : Être dans le TOP 5 des meilleurs EMF au Cameroun\\n- Leadership : PCA Eugène Nzouadjeu Yota, DG Bertin Tonleu\\n- Partenaire stratégique : Finafrica depuis 2019\\n\\nCONTACT :\\n- Téléphone : +237 695 021 141\\n- Email : <EMAIL>\\n- Site web : www.focep.cm\\n- Siège : Yaoundé, Cameroun\\n\\nINSTRUCTIONS DE COMPORTEMENT :\\n1. Sois toujours poli, professionnel et empathique\\n2. Utilise un langage chaleureux et personnalisé\\n3. Fournis des informations précises et factuelles uniquement\\n4. Si tu ne connais pas une information, oriente vers un agent humain\\n5. Utilise des émojis appropriés pour rendre les réponses attrayantes\\n6. Adapte ton niveau de langage au contexte bancaire/financier\\n7. Reste dans le contexte de FOCEP SA - ne réponds pas aux questions hors sujet\\n8. Encourage toujours l'engagement client et la satisfaction\\n\\nPRODUITS ET SERVICES DISPONIBLES :\\n- Compte d'épargne: Compte à vue qui permet à son titulaire physique ou morale (associations, GIC, GIE) de sécuriser son...\\n- Compte courant et compte de dépôt simple: Comptes destinés aux commerçants et particuliers sollicitant bénéficier des services bancaires pour ...\\n- Bons de caisse: C'est l'opportunité de placement de fonds offerte par FOCEP à sa clientèle. Ce produit permet aux cl...\\n- Crédits aux jeunes entrepreneurs: Les crédits aux jeunes entrepreneurs pour le financement des activités agro-pastorales et des chaîne...\\n- Crédits aux commerçants et assimilés: Crédits pour le renforcement du fonds de roulement des commerçants et assimilés...\\n- Crédits aux fonctionnaires et employés d'entreprises: Crédits pour le financement des besoins des fonctionnaires et employés d'entreprises domiciliées et ...\\n- Crédit scolaire: Crédit spécialement conçu pour financer les frais de scolarité et fournitures scolaires...\\n- Crédit agricole saisonnier: Financement des activités agricoles selon les saisons de culture...\\n\\nAGENCES PRINCIPALES :\\n- FOCEP Yaoundé Centre (Yaoundé): +237 222 23 14 83\\n- FOCEP Mokolo (Yaoundé): +237 222 23 14 84\\n- FOCEP Douala Ndokoti (Douala): +237 233 42 15 67\\n- FOCEP Douala Akwa (Douala): +237 233 42 15 68\\n- FOCEP Douala Mboppi (Douala): +237 233 42 15 69\\n\\nCONTEXTE CLIENT PERSONNALISÉ :\\n- Niveau de satisfaction : neutral\\n\\nINSTRUCTIONS SPÉCIALES :\\n- Utilise ce contexte pour personnaliser ta réponse\\n- Fais référence aux interactions précédentes si pertinent\\n- Adapte ton niveau de détail selon le type de client\\n- Si le client a demandé un agent humain, propose de le mettre en relation\\n- Maintiens un ton cohérent avec le niveau de satisfaction détecté\\n- Génère une réponse naturelle, empathique et professionnelle\\n- Utilise des émojis appropriés pour rendre la réponse attrayante\\n- Reste factuel et ne invente jamais d'informations sur FOCEP SA\"},{\"role\":\"user\",\"content\":\"Bonjour\\n\\n[Contexte: Intent détecté: greeting]\"}],\"max_tokens\":1000,\"temperature\":0.7,\"stream\":false}","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3487","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"https://api.deepseek.com/v1/chat/completions","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"data":{"error":{"code":"invalid_request_error","message":"Insufficient Balance","param":null,"type":"unknown_error"}},"headers":{"access-control-allow-credentials":"true","cf-cache-status":"DYNAMIC","cf-ray":"965d1ef1db1ad07a-LHR","connection":"keep-alive","content-length":"111","content-type":"application/json","date":"Sun, 27 Jul 2025 15:20:27 GMT","server":"cloudflare","set-cookie":"__cf_bm=qQ2yuIvrZHTRYqaOf1j0FoHatnHNyJ3m7yir1jl7S3M-1753629627-*******-m6Cl3m1VNRbV1huVxJaXxsb2728DVMJwmkVPZ7sfPoroNSEHacrcG9IczMTKI9JERX1bgObX_qYYgWCO88UvRXX_ifo6k.LtXlMMVmvdxlU; path=/; expires=Sun, 27-Jul-25 15:50:27 GMT; domain=.deepseek.com; HttpOnly; Secure; SameSite=None","strict-transport-security":"max-age=31536000; includeSubDomains; preload","vary":"origin, access-control-request-method, access-control-request-headers","x-content-type-options":"nosniff","x-ds-trace-id":"fe0f6da675f6d3210e9fc37f1b104bd7"},"request":{"_closed":true,"_contentLength":3487,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer sk-faed2ca2fb944a91a52266d4db96110c\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 3487\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: api.deepseek.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":false,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://api.deepseek.com/v1/chat/completions","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3487","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"hostname":"api.deepseek.com","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/v1/chat/completions","pathname":"/v1/chat/completions","port":"","protocol":"https:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":3487,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"api.deepseek.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/v1/chat/completions","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Sun, 27 Jul 2025 15:20:27 GMT","Content-Type","application/json","Content-Length","111","Connection","keep-alive","vary","origin, access-control-request-method, access-control-request-headers","access-control-allow-credentials","true","x-ds-trace-id","fe0f6da675f6d3210e9fc37f1b104bd7","Strict-Transport-Security","max-age=31536000; includeSubDomains; preload","X-Content-Type-Options","nosniff","cf-cache-status","DYNAMIC","Set-Cookie","__cf_bm=qQ2yuIvrZHTRYqaOf1j0FoHatnHNyJ3m7yir1jl7S3M-1753629627-*******-m6Cl3m1VNRbV1huVxJaXxsb2728DVMJwmkVPZ7sfPoroNSEHacrcG9IczMTKI9JERX1bgObX_qYYgWCO88UvRXX_ifo6k.LtXlMMVmvdxlU; path=/; expires=Sun, 27-Jul-25 15:50:27 GMT; domain=.deepseek.com; HttpOnly; Secure; SameSite=None","Server","cloudflare","CF-RAY","965d1ef1db1ad07a-LHR"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://api.deepseek.com/v1/chat/completions","socket":null,"statusCode":402,"statusMessage":"Payment Required","upgrade":false,"url":""},"reusedSocket":true,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"status":402,"statusText":"Payment Required"},"service":"focep-chatbot","stack":"AxiosError: Request failed with status code 402\n    at settle (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DeepSeekService.makeAPICall (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\deepSeekService.js:263:22)\n    at async DeepSeekService.generateContextualResponse (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\deepSeekService.js:321:24)\n    at async ResponseGenerator.generateDeepSeekResponse (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\responseGenerator.js:303:24)\n    at async ResponseGenerator.generateResponse (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\responseGenerator.js:99:36)\n    at async C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:176:22\n    at async DeepSeekIntegrationValidator.test (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:289:22)\n    at async DeepSeekIntegrationValidator.validateIntegration (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:166:5)\n    at async DeepSeekIntegrationValidator.runValidation (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:35:7)","status":402,"timestamp":"2025-07-27 17:20:27"}
{"code":"ERR_BAD_REQUEST","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":"{\"model\":\"deepseek-chat\",\"messages\":[{\"role\":\"system\",\"content\":\"Tu es l'assistant virtuel intelligent de FOCEP SA (Fonds Camerounais d'Épargne et de Crédit pour la Production), une institution de microfinance camerounaise de référence.\\n\\nINFORMATIONS IMPORTANTES SUR FOCEP SA :\\n- Fondée en 2007, 18 ans d'expérience\\n- 250 employés, 15 agences\\n- Plus de 50 000 clients satisfaits\\n- Capital : 1.5 milliard FCFA\\n- Classement : Parmi les 10 meilleurs EMF du Cameroun\\n- Objectif 2027 : Être dans le TOP 5 des meilleurs EMF au Cameroun\\n- Leadership : PCA Eugène Nzouadjeu Yota, DG Bertin Tonleu\\n- Partenaire stratégique : Finafrica depuis 2019\\n\\nCONTACT :\\n- Téléphone : +237 695 021 141\\n- Email : <EMAIL>\\n- Site web : www.focep.cm\\n- Siège : Yaoundé, Cameroun\\n\\nINSTRUCTIONS DE COMPORTEMENT :\\n1. Sois toujours poli, professionnel et empathique\\n2. Utilise un langage chaleureux et personnalisé\\n3. Fournis des informations précises et factuelles uniquement\\n4. Si tu ne connais pas une information, oriente vers un agent humain\\n5. Utilise des émojis appropriés pour rendre les réponses attrayantes\\n6. Adapte ton niveau de langage au contexte bancaire/financier\\n7. Reste dans le contexte de FOCEP SA - ne réponds pas aux questions hors sujet\\n8. Encourage toujours l'engagement client et la satisfaction\\n\\nPRODUITS ET SERVICES DISPONIBLES :\\n- Compte d'épargne: Compte à vue qui permet à son titulaire physique ou morale (associations, GIC, GIE) de sécuriser son...\\n- Compte courant et compte de dépôt simple: Comptes destinés aux commerçants et particuliers sollicitant bénéficier des services bancaires pour ...\\n- Bons de caisse: C'est l'opportunité de placement de fonds offerte par FOCEP à sa clientèle. Ce produit permet aux cl...\\n- Crédits aux jeunes entrepreneurs: Les crédits aux jeunes entrepreneurs pour le financement des activités agro-pastorales et des chaîne...\\n- Crédits aux commerçants et assimilés: Crédits pour le renforcement du fonds de roulement des commerçants et assimilés...\\n- Crédits aux fonctionnaires et employés d'entreprises: Crédits pour le financement des besoins des fonctionnaires et employés d'entreprises domiciliées et ...\\n- Crédit scolaire: Crédit spécialement conçu pour financer les frais de scolarité et fournitures scolaires...\\n- Crédit agricole saisonnier: Financement des activités agricoles selon les saisons de culture...\\n\\nAGENCES PRINCIPALES :\\n- FOCEP Yaoundé Centre (Yaoundé): +237 222 23 14 83\\n- FOCEP Mokolo (Yaoundé): +237 222 23 14 84\\n- FOCEP Douala Ndokoti (Douala): +237 233 42 15 67\\n- FOCEP Douala Akwa (Douala): +237 233 42 15 68\\n- FOCEP Douala Mboppi (Douala): +237 233 42 15 69\\n\\nCONTEXTE CLIENT PERSONNALISÉ :\\n- Niveau de satisfaction : neutral\\n\\nINSTRUCTIONS SPÉCIALES :\\n- Utilise ce contexte pour personnaliser ta réponse\\n- Fais référence aux interactions précédentes si pertinent\\n- Adapte ton niveau de détail selon le type de client\\n- Si le client a demandé un agent humain, propose de le mettre en relation\\n- Maintiens un ton cohérent avec le niveau de satisfaction détecté\\n- Génère une réponse naturelle, empathique et professionnelle\\n- Utilise des émojis appropriés pour rendre la réponse attrayante\\n- Reste factuel et ne invente jamais d'informations sur FOCEP SA\"},{\"role\":\"user\",\"content\":\"Bonjour\\n\\n[Contexte: Intent détecté: greeting]\"}],\"max_tokens\":1000,\"temperature\":0.7,\"stream\":false}","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3487","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"https://api.deepseek.com/v1/chat/completions","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"level":"error","message":"Erreur lors de la génération DeepSeek: Request failed with status code 402","name":"AxiosError","request":{"_closed":true,"_contentLength":3487,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer sk-faed2ca2fb944a91a52266d4db96110c\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 3487\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: api.deepseek.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":false,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://api.deepseek.com/v1/chat/completions","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3487","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"hostname":"api.deepseek.com","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/v1/chat/completions","pathname":"/v1/chat/completions","port":"","protocol":"https:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":3487,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"api.deepseek.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/v1/chat/completions","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Sun, 27 Jul 2025 15:20:27 GMT","Content-Type","application/json","Content-Length","111","Connection","keep-alive","vary","origin, access-control-request-method, access-control-request-headers","access-control-allow-credentials","true","x-ds-trace-id","fe0f6da675f6d3210e9fc37f1b104bd7","Strict-Transport-Security","max-age=31536000; includeSubDomains; preload","X-Content-Type-Options","nosniff","cf-cache-status","DYNAMIC","Set-Cookie","__cf_bm=qQ2yuIvrZHTRYqaOf1j0FoHatnHNyJ3m7yir1jl7S3M-1753629627-*******-m6Cl3m1VNRbV1huVxJaXxsb2728DVMJwmkVPZ7sfPoroNSEHacrcG9IczMTKI9JERX1bgObX_qYYgWCO88UvRXX_ifo6k.LtXlMMVmvdxlU; path=/; expires=Sun, 27-Jul-25 15:50:27 GMT; domain=.deepseek.com; HttpOnly; Secure; SameSite=None","Server","cloudflare","CF-RAY","965d1ef1db1ad07a-LHR"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://api.deepseek.com/v1/chat/completions","socket":null,"statusCode":402,"statusMessage":"Payment Required","upgrade":false,"url":""},"reusedSocket":true,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"response":{"config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":"{\"model\":\"deepseek-chat\",\"messages\":[{\"role\":\"system\",\"content\":\"Tu es l'assistant virtuel intelligent de FOCEP SA (Fonds Camerounais d'Épargne et de Crédit pour la Production), une institution de microfinance camerounaise de référence.\\n\\nINFORMATIONS IMPORTANTES SUR FOCEP SA :\\n- Fondée en 2007, 18 ans d'expérience\\n- 250 employés, 15 agences\\n- Plus de 50 000 clients satisfaits\\n- Capital : 1.5 milliard FCFA\\n- Classement : Parmi les 10 meilleurs EMF du Cameroun\\n- Objectif 2027 : Être dans le TOP 5 des meilleurs EMF au Cameroun\\n- Leadership : PCA Eugène Nzouadjeu Yota, DG Bertin Tonleu\\n- Partenaire stratégique : Finafrica depuis 2019\\n\\nCONTACT :\\n- Téléphone : +237 695 021 141\\n- Email : <EMAIL>\\n- Site web : www.focep.cm\\n- Siège : Yaoundé, Cameroun\\n\\nINSTRUCTIONS DE COMPORTEMENT :\\n1. Sois toujours poli, professionnel et empathique\\n2. Utilise un langage chaleureux et personnalisé\\n3. Fournis des informations précises et factuelles uniquement\\n4. Si tu ne connais pas une information, oriente vers un agent humain\\n5. Utilise des émojis appropriés pour rendre les réponses attrayantes\\n6. Adapte ton niveau de langage au contexte bancaire/financier\\n7. Reste dans le contexte de FOCEP SA - ne réponds pas aux questions hors sujet\\n8. Encourage toujours l'engagement client et la satisfaction\\n\\nPRODUITS ET SERVICES DISPONIBLES :\\n- Compte d'épargne: Compte à vue qui permet à son titulaire physique ou morale (associations, GIC, GIE) de sécuriser son...\\n- Compte courant et compte de dépôt simple: Comptes destinés aux commerçants et particuliers sollicitant bénéficier des services bancaires pour ...\\n- Bons de caisse: C'est l'opportunité de placement de fonds offerte par FOCEP à sa clientèle. Ce produit permet aux cl...\\n- Crédits aux jeunes entrepreneurs: Les crédits aux jeunes entrepreneurs pour le financement des activités agro-pastorales et des chaîne...\\n- Crédits aux commerçants et assimilés: Crédits pour le renforcement du fonds de roulement des commerçants et assimilés...\\n- Crédits aux fonctionnaires et employés d'entreprises: Crédits pour le financement des besoins des fonctionnaires et employés d'entreprises domiciliées et ...\\n- Crédit scolaire: Crédit spécialement conçu pour financer les frais de scolarité et fournitures scolaires...\\n- Crédit agricole saisonnier: Financement des activités agricoles selon les saisons de culture...\\n\\nAGENCES PRINCIPALES :\\n- FOCEP Yaoundé Centre (Yaoundé): +237 222 23 14 83\\n- FOCEP Mokolo (Yaoundé): +237 222 23 14 84\\n- FOCEP Douala Ndokoti (Douala): +237 233 42 15 67\\n- FOCEP Douala Akwa (Douala): +237 233 42 15 68\\n- FOCEP Douala Mboppi (Douala): +237 233 42 15 69\\n\\nCONTEXTE CLIENT PERSONNALISÉ :\\n- Niveau de satisfaction : neutral\\n\\nINSTRUCTIONS SPÉCIALES :\\n- Utilise ce contexte pour personnaliser ta réponse\\n- Fais référence aux interactions précédentes si pertinent\\n- Adapte ton niveau de détail selon le type de client\\n- Si le client a demandé un agent humain, propose de le mettre en relation\\n- Maintiens un ton cohérent avec le niveau de satisfaction détecté\\n- Génère une réponse naturelle, empathique et professionnelle\\n- Utilise des émojis appropriés pour rendre la réponse attrayante\\n- Reste factuel et ne invente jamais d'informations sur FOCEP SA\"},{\"role\":\"user\",\"content\":\"Bonjour\\n\\n[Contexte: Intent détecté: greeting]\"}],\"max_tokens\":1000,\"temperature\":0.7,\"stream\":false}","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3487","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"https://api.deepseek.com/v1/chat/completions","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"data":{"error":{"code":"invalid_request_error","message":"Insufficient Balance","param":null,"type":"unknown_error"}},"headers":{"access-control-allow-credentials":"true","cf-cache-status":"DYNAMIC","cf-ray":"965d1ef1db1ad07a-LHR","connection":"keep-alive","content-length":"111","content-type":"application/json","date":"Sun, 27 Jul 2025 15:20:27 GMT","server":"cloudflare","set-cookie":"__cf_bm=qQ2yuIvrZHTRYqaOf1j0FoHatnHNyJ3m7yir1jl7S3M-1753629627-*******-m6Cl3m1VNRbV1huVxJaXxsb2728DVMJwmkVPZ7sfPoroNSEHacrcG9IczMTKI9JERX1bgObX_qYYgWCO88UvRXX_ifo6k.LtXlMMVmvdxlU; path=/; expires=Sun, 27-Jul-25 15:50:27 GMT; domain=.deepseek.com; HttpOnly; Secure; SameSite=None","strict-transport-security":"max-age=31536000; includeSubDomains; preload","vary":"origin, access-control-request-method, access-control-request-headers","x-content-type-options":"nosniff","x-ds-trace-id":"fe0f6da675f6d3210e9fc37f1b104bd7"},"request":{"_closed":true,"_contentLength":3487,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer sk-faed2ca2fb944a91a52266d4db96110c\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 3487\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: api.deepseek.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":false,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://api.deepseek.com/v1/chat/completions","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3487","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"hostname":"api.deepseek.com","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/v1/chat/completions","pathname":"/v1/chat/completions","port":"","protocol":"https:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":3487,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"api.deepseek.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/v1/chat/completions","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Sun, 27 Jul 2025 15:20:27 GMT","Content-Type","application/json","Content-Length","111","Connection","keep-alive","vary","origin, access-control-request-method, access-control-request-headers","access-control-allow-credentials","true","x-ds-trace-id","fe0f6da675f6d3210e9fc37f1b104bd7","Strict-Transport-Security","max-age=31536000; includeSubDomains; preload","X-Content-Type-Options","nosniff","cf-cache-status","DYNAMIC","Set-Cookie","__cf_bm=qQ2yuIvrZHTRYqaOf1j0FoHatnHNyJ3m7yir1jl7S3M-1753629627-*******-m6Cl3m1VNRbV1huVxJaXxsb2728DVMJwmkVPZ7sfPoroNSEHacrcG9IczMTKI9JERX1bgObX_qYYgWCO88UvRXX_ifo6k.LtXlMMVmvdxlU; path=/; expires=Sun, 27-Jul-25 15:50:27 GMT; domain=.deepseek.com; HttpOnly; Secure; SameSite=None","Server","cloudflare","CF-RAY","965d1ef1db1ad07a-LHR"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://api.deepseek.com/v1/chat/completions","socket":null,"statusCode":402,"statusMessage":"Payment Required","upgrade":false,"url":""},"reusedSocket":true,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"status":402,"statusText":"Payment Required"},"service":"focep-chatbot","stack":"AxiosError: Request failed with status code 402\n    at settle (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DeepSeekService.makeAPICall (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\deepSeekService.js:263:22)\n    at async DeepSeekService.generateContextualResponse (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\deepSeekService.js:321:24)\n    at async ResponseGenerator.generateDeepSeekResponse (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\responseGenerator.js:303:24)\n    at async ResponseGenerator.generateResponse (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\responseGenerator.js:99:36)\n    at async C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:176:22\n    at async DeepSeekIntegrationValidator.test (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:289:22)\n    at async DeepSeekIntegrationValidator.validateIntegration (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:166:5)\n    at async DeepSeekIntegrationValidator.runValidation (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:35:7)","status":402,"timestamp":"2025-07-27 17:20:27"}
{"context":{"conversation":{"id":"test-conv","messages":[]},"customer":{"preferredLanguage":"fr"},"intent":"greeting","language":"fr","text":"Bonjour"},"level":"error","message":"Réponse d'urgence activée","service":"focep-chatbot","timestamp":"2025-07-27 17:20:27"}
{"context":{"intent":"greeting","language":"fr","text":"Test message"},"level":"error","message":"Réponse d'urgence activée","service":"focep-chatbot","timestamp":"2025-07-27 17:20:27"}
{"code":"ERR_BAD_REQUEST","component":"nlp","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":"{\"model\":\"deepseek-chat\",\"messages\":[{\"role\":\"system\",\"content\":\"Tu es l'assistant virtuel intelligent de FOCEP SA (Fonds Camerounais d'Épargne et de Crédit pour la Production), une institution de microfinance camerounaise de référence.\\n\\nINFORMATIONS IMPORTANTES SUR FOCEP SA :\\n- Fondée en 2007, 18 ans d'expérience\\n- 250 employés, 15 agences\\n- Plus de 50 000 clients satisfaits\\n- Capital : 1.5 milliard FCFA\\n- Classement : Parmi les 10 meilleurs EMF du Cameroun\\n- Objectif 2027 : Être dans le TOP 5 des meilleurs EMF au Cameroun\\n- Leadership : PCA Eugène Nzouadjeu Yota, DG Bertin Tonleu\\n- Partenaire stratégique : Finafrica depuis 2019\\n\\nCONTACT :\\n- Téléphone : +237 695 021 141\\n- Email : <EMAIL>\\n- Site web : www.focep.cm\\n- Siège : Yaoundé, Cameroun\\n\\nINSTRUCTIONS DE COMPORTEMENT :\\n1. Sois toujours poli, professionnel et empathique\\n2. Utilise un langage chaleureux et personnalisé\\n3. Fournis des informations précises et factuelles uniquement\\n4. Si tu ne connais pas une information, oriente vers un agent humain\\n5. Utilise des émojis appropriés pour rendre les réponses attrayantes\\n6. Adapte ton niveau de langage au contexte bancaire/financier\\n7. Reste dans le contexte de FOCEP SA - ne réponds pas aux questions hors sujet\\n8. Encourage toujours l'engagement client et la satisfaction\\n\\nPRODUITS ET SERVICES DISPONIBLES :\\n- Compte d'épargne: Compte à vue qui permet à son titulaire physique ou morale (associations, GIC, GIE) de sécuriser son...\\n- Compte courant et compte de dépôt simple: Comptes destinés aux commerçants et particuliers sollicitant bénéficier des services bancaires pour ...\\n- Bons de caisse: C'est l'opportunité de placement de fonds offerte par FOCEP à sa clientèle. Ce produit permet aux cl...\\n- Crédits aux jeunes entrepreneurs: Les crédits aux jeunes entrepreneurs pour le financement des activités agro-pastorales et des chaîne...\\n- Crédits aux commerçants et assimilés: Crédits pour le renforcement du fonds de roulement des commerçants et assimilés...\\n- Crédits aux fonctionnaires et employés d'entreprises: Crédits pour le financement des besoins des fonctionnaires et employés d'entreprises domiciliées et ...\\n- Crédit scolaire: Crédit spécialement conçu pour financer les frais de scolarité et fournitures scolaires...\\n- Crédit agricole saisonnier: Financement des activités agricoles selon les saisons de culture...\\n\\nAGENCES PRINCIPALES :\\n- FOCEP Yaoundé Centre (Yaoundé): +237 222 23 14 83\\n- FOCEP Mokolo (Yaoundé): +237 222 23 14 84\\n- FOCEP Douala Ndokoti (Douala): +237 233 42 15 67\\n- FOCEP Douala Akwa (Douala): +237 233 42 15 68\\n- FOCEP Douala Mboppi (Douala): +237 233 42 15 69\\n\\nAnalyse ce message et retourne UNIQUEMENT un objet JSON avec cette structure exacte :\\n{\\n  \\\"intent\\\": \\\"intention_detectee\\\",\\n  \\\"confidence\\\": 0.95,\\n  \\\"language\\\": \\\"fr\\\",\\n  \\\"entities\\\": [{\\\"entity\\\": \\\"type\\\", \\\"value\\\": \\\"valeur\\\", \\\"confidence\\\": 0.9}],\\n  \\\"sentiment\\\": \\\"positive|negative|neutral\\\",\\n  \\\"sentimentScore\\\": 0.5,\\n  \\\"context\\\": \\\"contexte_conversationnel\\\",\\n  \\\"needsHumanAgent\\\": false\\n}\"},{\"role\":\"user\",\"content\":\"Test de performance\"}],\"max_tokens\":500,\"temperature\":0.3,\"stream\":false}","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3256","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"https://api.deepseek.com/v1/chat/completions","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"level":"error","message":"Erreur lors de l'analyse DeepSeek: Request failed with status code 402","name":"AxiosError","request":{"_closed":true,"_contentLength":3256,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer sk-faed2ca2fb944a91a52266d4db96110c\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 3256\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: api.deepseek.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":false,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://api.deepseek.com/v1/chat/completions","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3256","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"hostname":"api.deepseek.com","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/v1/chat/completions","pathname":"/v1/chat/completions","port":"","protocol":"https:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":3256,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"api.deepseek.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/v1/chat/completions","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Sun, 27 Jul 2025 15:20:28 GMT","Content-Type","application/json","Content-Length","111","Connection","keep-alive","vary","origin, access-control-request-method, access-control-request-headers","access-control-allow-credentials","true","x-ds-trace-id","781c69eac51d9d4d2088ed260b167998","Strict-Transport-Security","max-age=31536000; includeSubDomains; preload","X-Content-Type-Options","nosniff","cf-cache-status","DYNAMIC","Set-Cookie","__cf_bm=A6S.****************************************1753629628-*******-sbNA4ik3wFV35NpW4Vz7A_cDgJKHv3dJgDzFK41gKqqUHaQggPTwYuP1mmuCpudxJgrJf2DWKAlt0p.Tm8CL5ZSHj3AccJWHT1wU3QJmm8M; path=/; expires=Sun, 27-Jul-25 15:50:28 GMT; domain=.deepseek.com; HttpOnly; Secure; SameSite=None","Server","cloudflare","CF-RAY","965d1ef96c1dd07a-LHR"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://api.deepseek.com/v1/chat/completions","socket":null,"statusCode":402,"statusMessage":"Payment Required","upgrade":false,"url":""},"reusedSocket":true,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"response":{"config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":"{\"model\":\"deepseek-chat\",\"messages\":[{\"role\":\"system\",\"content\":\"Tu es l'assistant virtuel intelligent de FOCEP SA (Fonds Camerounais d'Épargne et de Crédit pour la Production), une institution de microfinance camerounaise de référence.\\n\\nINFORMATIONS IMPORTANTES SUR FOCEP SA :\\n- Fondée en 2007, 18 ans d'expérience\\n- 250 employés, 15 agences\\n- Plus de 50 000 clients satisfaits\\n- Capital : 1.5 milliard FCFA\\n- Classement : Parmi les 10 meilleurs EMF du Cameroun\\n- Objectif 2027 : Être dans le TOP 5 des meilleurs EMF au Cameroun\\n- Leadership : PCA Eugène Nzouadjeu Yota, DG Bertin Tonleu\\n- Partenaire stratégique : Finafrica depuis 2019\\n\\nCONTACT :\\n- Téléphone : +237 695 021 141\\n- Email : <EMAIL>\\n- Site web : www.focep.cm\\n- Siège : Yaoundé, Cameroun\\n\\nINSTRUCTIONS DE COMPORTEMENT :\\n1. Sois toujours poli, professionnel et empathique\\n2. Utilise un langage chaleureux et personnalisé\\n3. Fournis des informations précises et factuelles uniquement\\n4. Si tu ne connais pas une information, oriente vers un agent humain\\n5. Utilise des émojis appropriés pour rendre les réponses attrayantes\\n6. Adapte ton niveau de langage au contexte bancaire/financier\\n7. Reste dans le contexte de FOCEP SA - ne réponds pas aux questions hors sujet\\n8. Encourage toujours l'engagement client et la satisfaction\\n\\nPRODUITS ET SERVICES DISPONIBLES :\\n- Compte d'épargne: Compte à vue qui permet à son titulaire physique ou morale (associations, GIC, GIE) de sécuriser son...\\n- Compte courant et compte de dépôt simple: Comptes destinés aux commerçants et particuliers sollicitant bénéficier des services bancaires pour ...\\n- Bons de caisse: C'est l'opportunité de placement de fonds offerte par FOCEP à sa clientèle. Ce produit permet aux cl...\\n- Crédits aux jeunes entrepreneurs: Les crédits aux jeunes entrepreneurs pour le financement des activités agro-pastorales et des chaîne...\\n- Crédits aux commerçants et assimilés: Crédits pour le renforcement du fonds de roulement des commerçants et assimilés...\\n- Crédits aux fonctionnaires et employés d'entreprises: Crédits pour le financement des besoins des fonctionnaires et employés d'entreprises domiciliées et ...\\n- Crédit scolaire: Crédit spécialement conçu pour financer les frais de scolarité et fournitures scolaires...\\n- Crédit agricole saisonnier: Financement des activités agricoles selon les saisons de culture...\\n\\nAGENCES PRINCIPALES :\\n- FOCEP Yaoundé Centre (Yaoundé): +237 222 23 14 83\\n- FOCEP Mokolo (Yaoundé): +237 222 23 14 84\\n- FOCEP Douala Ndokoti (Douala): +237 233 42 15 67\\n- FOCEP Douala Akwa (Douala): +237 233 42 15 68\\n- FOCEP Douala Mboppi (Douala): +237 233 42 15 69\\n\\nAnalyse ce message et retourne UNIQUEMENT un objet JSON avec cette structure exacte :\\n{\\n  \\\"intent\\\": \\\"intention_detectee\\\",\\n  \\\"confidence\\\": 0.95,\\n  \\\"language\\\": \\\"fr\\\",\\n  \\\"entities\\\": [{\\\"entity\\\": \\\"type\\\", \\\"value\\\": \\\"valeur\\\", \\\"confidence\\\": 0.9}],\\n  \\\"sentiment\\\": \\\"positive|negative|neutral\\\",\\n  \\\"sentimentScore\\\": 0.5,\\n  \\\"context\\\": \\\"contexte_conversationnel\\\",\\n  \\\"needsHumanAgent\\\": false\\n}\"},{\"role\":\"user\",\"content\":\"Test de performance\"}],\"max_tokens\":500,\"temperature\":0.3,\"stream\":false}","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3256","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"https://api.deepseek.com/v1/chat/completions","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"data":{"error":{"code":"invalid_request_error","message":"Insufficient Balance","param":null,"type":"unknown_error"}},"headers":{"access-control-allow-credentials":"true","cf-cache-status":"DYNAMIC","cf-ray":"965d1ef96c1dd07a-LHR","connection":"keep-alive","content-length":"111","content-type":"application/json","date":"Sun, 27 Jul 2025 15:20:28 GMT","server":"cloudflare","set-cookie":"__cf_bm=A6S.****************************************1753629628-*******-sbNA4ik3wFV35NpW4Vz7A_cDgJKHv3dJgDzFK41gKqqUHaQggPTwYuP1mmuCpudxJgrJf2DWKAlt0p.Tm8CL5ZSHj3AccJWHT1wU3QJmm8M; path=/; expires=Sun, 27-Jul-25 15:50:28 GMT; domain=.deepseek.com; HttpOnly; Secure; SameSite=None","strict-transport-security":"max-age=31536000; includeSubDomains; preload","vary":"origin, access-control-request-method, access-control-request-headers","x-content-type-options":"nosniff","x-ds-trace-id":"781c69eac51d9d4d2088ed260b167998"},"request":{"_closed":true,"_contentLength":3256,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer sk-faed2ca2fb944a91a52266d4db96110c\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 3256\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: api.deepseek.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":false,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://api.deepseek.com/v1/chat/completions","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3256","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"hostname":"api.deepseek.com","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/v1/chat/completions","pathname":"/v1/chat/completions","port":"","protocol":"https:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":3256,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"api.deepseek.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/v1/chat/completions","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":null,"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Sun, 27 Jul 2025 15:20:28 GMT","Content-Type","application/json","Content-Length","111","Connection","keep-alive","vary","origin, access-control-request-method, access-control-request-headers","access-control-allow-credentials","true","x-ds-trace-id","781c69eac51d9d4d2088ed260b167998","Strict-Transport-Security","max-age=31536000; includeSubDomains; preload","X-Content-Type-Options","nosniff","cf-cache-status","DYNAMIC","Set-Cookie","__cf_bm=A6S.****************************************1753629628-*******-sbNA4ik3wFV35NpW4Vz7A_cDgJKHv3dJgDzFK41gKqqUHaQggPTwYuP1mmuCpudxJgrJf2DWKAlt0p.Tm8CL5ZSHj3AccJWHT1wU3QJmm8M; path=/; expires=Sun, 27-Jul-25 15:50:28 GMT; domain=.deepseek.com; HttpOnly; Secure; SameSite=None","Server","cloudflare","CF-RAY","965d1ef96c1dd07a-LHR"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://api.deepseek.com/v1/chat/completions","socket":null,"statusCode":402,"statusMessage":"Payment Required","upgrade":false,"url":""},"reusedSocket":true,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"status":402,"statusText":"Payment Required"},"service":"focep-chatbot","stack":"AxiosError: Request failed with status code 402\n    at settle (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DeepSeekService.makeAPICall (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\deepSeekService.js:263:22)\n    at async DeepSeekService.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\deepSeekService.js:184:24)\n    at async NLPService.analyzeWithDeepSeek (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\nlpService.js:511:22)\n    at async NLPService.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\nlpService.js:455:36)\n    at async C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:253:7\n    at async DeepSeekIntegrationValidator.test (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:289:22)\n    at async DeepSeekIntegrationValidator.validatePerformance (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:250:5)\n    at async DeepSeekIntegrationValidator.runValidation (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:37:7)","status":402,"timestamp":"2025-07-27 17:20:28"}
{"code":"ERR_BAD_REQUEST","component":"nlp","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":"{\"model\":\"deepseek-chat\",\"messages\":[{\"role\":\"system\",\"content\":\"Tu es l'assistant virtuel intelligent de FOCEP SA (Fonds Camerounais d'Épargne et de Crédit pour la Production), une institution de microfinance camerounaise de référence.\\n\\nINFORMATIONS IMPORTANTES SUR FOCEP SA :\\n- Fondée en 2007, 18 ans d'expérience\\n- 250 employés, 15 agences\\n- Plus de 50 000 clients satisfaits\\n- Capital : 1.5 milliard FCFA\\n- Classement : Parmi les 10 meilleurs EMF du Cameroun\\n- Objectif 2027 : Être dans le TOP 5 des meilleurs EMF au Cameroun\\n- Leadership : PCA Eugène Nzouadjeu Yota, DG Bertin Tonleu\\n- Partenaire stratégique : Finafrica depuis 2019\\n\\nCONTACT :\\n- Téléphone : +237 695 021 141\\n- Email : <EMAIL>\\n- Site web : www.focep.cm\\n- Siège : Yaoundé, Cameroun\\n\\nINSTRUCTIONS DE COMPORTEMENT :\\n1. Sois toujours poli, professionnel et empathique\\n2. Utilise un langage chaleureux et personnalisé\\n3. Fournis des informations précises et factuelles uniquement\\n4. Si tu ne connais pas une information, oriente vers un agent humain\\n5. Utilise des émojis appropriés pour rendre les réponses attrayantes\\n6. Adapte ton niveau de langage au contexte bancaire/financier\\n7. Reste dans le contexte de FOCEP SA - ne réponds pas aux questions hors sujet\\n8. Encourage toujours l'engagement client et la satisfaction\\n\\nPRODUITS ET SERVICES DISPONIBLES :\\n- Compte d'épargne: Compte à vue qui permet à son titulaire physique ou morale (associations, GIC, GIE) de sécuriser son...\\n- Compte courant et compte de dépôt simple: Comptes destinés aux commerçants et particuliers sollicitant bénéficier des services bancaires pour ...\\n- Bons de caisse: C'est l'opportunité de placement de fonds offerte par FOCEP à sa clientèle. Ce produit permet aux cl...\\n- Crédits aux jeunes entrepreneurs: Les crédits aux jeunes entrepreneurs pour le financement des activités agro-pastorales et des chaîne...\\n- Crédits aux commerçants et assimilés: Crédits pour le renforcement du fonds de roulement des commerçants et assimilés...\\n- Crédits aux fonctionnaires et employés d'entreprises: Crédits pour le financement des besoins des fonctionnaires et employés d'entreprises domiciliées et ...\\n- Crédit scolaire: Crédit spécialement conçu pour financer les frais de scolarité et fournitures scolaires...\\n- Crédit agricole saisonnier: Financement des activités agricoles selon les saisons de culture...\\n\\nAGENCES PRINCIPALES :\\n- FOCEP Yaoundé Centre (Yaoundé): +237 222 23 14 83\\n- FOCEP Mokolo (Yaoundé): +237 222 23 14 84\\n- FOCEP Douala Ndokoti (Douala): +237 233 42 15 67\\n- FOCEP Douala Akwa (Douala): +237 233 42 15 68\\n- FOCEP Douala Mboppi (Douala): +237 233 42 15 69\\n\\nAnalyse ce message et retourne UNIQUEMENT un objet JSON avec cette structure exacte :\\n{\\n  \\\"intent\\\": \\\"intention_detectee\\\",\\n  \\\"confidence\\\": 0.95,\\n  \\\"language\\\": \\\"fr\\\",\\n  \\\"entities\\\": [{\\\"entity\\\": \\\"type\\\", \\\"value\\\": \\\"valeur\\\", \\\"confidence\\\": 0.9}],\\n  \\\"sentiment\\\": \\\"positive|negative|neutral\\\",\\n  \\\"sentimentScore\\\": 0.5,\\n  \\\"context\\\": \\\"contexte_conversationnel\\\",\\n  \\\"needsHumanAgent\\\": false\\n}\"},{\"role\":\"user\",\"content\":\"Bonjour, je voudrais des informations sur vos crédits\"}],\"max_tokens\":500,\"temperature\":0.3,\"stream\":false}","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3291","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"https://api.deepseek.com/v1/chat/completions","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"level":"error","message":"Erreur lors de l'analyse DeepSeek: Request failed with status code 402","name":"AxiosError","request":{"_closed":true,"_contentLength":3291,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer sk-faed2ca2fb944a91a52266d4db96110c\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 3291\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: api.deepseek.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":false,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://api.deepseek.com/v1/chat/completions","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3291","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"hostname":"api.deepseek.com","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["api.deepseek.com:443:::::::::::::::::::::"],"map":{"api.deepseek.com:443:::::::::::::::::::::":{"data":[48,130,5,30,2,1,1,2,2,3,4,4,2,19,2,4,32,182,170,68,151,59,40,242,9,118,166,208,53,152,221,97,83,144,207,51,135,16,80,0,146,191,53,236,54,94,192,246,212,4,48,173,49,217,13,13,145,113,223,195,248,2,96,2,56,236,6,195,102,135,93,130,106,143,132,197,14,119,73,87,213,90,247,76,92,143,52,44,150,228,173,64,177,185,74,66,133,31,135,161,6,2,4,104,134,71,129,162,4,2,2,28,32,163,130,3,164,48,130,3,160,48,130,3,69,160,3,2,1,2,2,17,0,163,190,105,37,120,24,205,248,14,170,212,88,243,140,138,57,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,50,49,49,52,49,52,50,54,90,23,13,50,53,49,48,49,57,49,53,49,52,50,53,90,48,27,49,25,48,23,6,3,85,4,3,19,16,97,112,105,46,100,101,101,112,115,101,101,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,51,118,226,200,6,188,68,18,176,52,138,127,29,17,87,125,247,7,64,158,97,41,227,140,204,191,154,42,152,60,238,73,48,241,141,115,242,12,40,255,141,164,140,169,43,3,11,207,148,218,43,142,115,41,64,112,236,215,88,92,202,202,116,49,163,130,2,72,48,130,2,68,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,76,172,145,127,138,85,102,65,47,11,145,32,46,99,30,234,20,147,94,209,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,111,55,52,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,27,6,3,85,29,17,4,20,48,18,130,16,97,112,105,46,100,101,101,112,115,101,101,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,90,107,106,122,85,88,75,67,85,119,85,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,13,225,242,48,43,211,13,193,64,98,18,9,234,85,46,252,71,116,124,177,215,233,48,239,14,66,30,180,126,78,170,52,0,0,1,152,45,140,236,168,0,0,4,3,0,71,48,69,2,32,72,79,96,141,144,75,147,4,51,127,123,74,239,99,45,217,184,103,13,54,148,126,252,80,147,167,13,3,83,131,63,128,2,33,0,136,195,28,197,208,172,236,4,44,102,112,89,117,71,185,113,111,37,67,156,234,173,255,143,246,176,237,141,88,98,29,122,0,117,0,18,241,78,52,189,83,114,76,132,6,25,195,143,63,122,19,248,231,181,98,135,136,156,109,48,5,132,235,229,134,38,58,0,0,1,152,45,140,236,158,0,0,4,3,0,70,48,68,2,32,81,78,14,41,157,76,141,23,134,250,133,130,103,51,75,237,150,33,80,56,5,61,189,199,190,229,27,255,139,90,159,152,2,32,4,29,66,139,108,115,254,210,107,109,245,70,72,81,160,166,223,49,136,52,77,39,126,136,191,145,229,102,156,27,136,85,48,10,6,8,42,134,72,206,61,4,3,2,3,73,0,48,70,2,33,0,155,180,227,173,100,123,239,205,90,249,188,164,148,101,108,13,241,27,216,13,210,181,6,87,145,117,56,238,206,54,223,66,2,33,0,239,159,142,70,124,211,172,47,136,33,5,79,21,178,150,177,140,243,186,185,123,255,113,47,245,252,196,97,121,100,100,209,164,2,4,0,166,18,4,16,97,112,105,46,100,101,101,112,115,101,101,107,46,99,111,109,169,5,2,3,0,253,31,170,129,211,4,129,208,3,183,109,0,145,198,192,109,21,173,177,169,106,71,158,230,62,16,57,182,211,143,241,157,202,156,17,231,61,172,220,37,130,25,217,42,61,101,115,222,65,31,182,202,150,247,81,191,12,195,209,245,41,109,205,88,217,123,69,28,225,122,162,126,70,12,158,79,118,196,145,146,195,238,144,46,96,180,97,66,187,239,200,134,138,129,229,168,66,80,173,135,61,77,144,184,213,158,141,142,23,97,83,103,83,95,47,43,44,5,244,132,146,176,168,13,3,235,131,3,169,131,104,233,163,53,162,128,110,175,91,175,188,70,117,142,5,17,245,143,213,202,150,169,159,52,39,129,32,203,207,19,162,104,249,58,156,199,134,48,119,66,147,43,18,248,140,248,170,133,35,162,63,153,119,100,55,110,9,62,92,99,118,12,19,4,248,222,214,255,164,137,165,82,234,167,98,91,177,237,47,139,164,249,170,198,86,61,174,7,2,5,0,229,0,215,26,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/v1/chat/completions","pathname":"/v1/chat/completions","port":"","protocol":"https:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":3291,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["api.deepseek.com:443:::::::::::::::::::::"],"map":{"api.deepseek.com:443:::::::::::::::::::::":{"data":[48,130,5,30,2,1,1,2,2,3,4,4,2,19,2,4,32,182,170,68,151,59,40,242,9,118,166,208,53,152,221,97,83,144,207,51,135,16,80,0,146,191,53,236,54,94,192,246,212,4,48,173,49,217,13,13,145,113,223,195,248,2,96,2,56,236,6,195,102,135,93,130,106,143,132,197,14,119,73,87,213,90,247,76,92,143,52,44,150,228,173,64,177,185,74,66,133,31,135,161,6,2,4,104,134,71,129,162,4,2,2,28,32,163,130,3,164,48,130,3,160,48,130,3,69,160,3,2,1,2,2,17,0,163,190,105,37,120,24,205,248,14,170,212,88,243,140,138,57,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,50,49,49,52,49,52,50,54,90,23,13,50,53,49,48,49,57,49,53,49,52,50,53,90,48,27,49,25,48,23,6,3,85,4,3,19,16,97,112,105,46,100,101,101,112,115,101,101,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,51,118,226,200,6,188,68,18,176,52,138,127,29,17,87,125,247,7,64,158,97,41,227,140,204,191,154,42,152,60,238,73,48,241,141,115,242,12,40,255,141,164,140,169,43,3,11,207,148,218,43,142,115,41,64,112,236,215,88,92,202,202,116,49,163,130,2,72,48,130,2,68,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,76,172,145,127,138,85,102,65,47,11,145,32,46,99,30,234,20,147,94,209,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,111,55,52,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,27,6,3,85,29,17,4,20,48,18,130,16,97,112,105,46,100,101,101,112,115,101,101,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,90,107,106,122,85,88,75,67,85,119,85,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,13,225,242,48,43,211,13,193,64,98,18,9,234,85,46,252,71,116,124,177,215,233,48,239,14,66,30,180,126,78,170,52,0,0,1,152,45,140,236,168,0,0,4,3,0,71,48,69,2,32,72,79,96,141,144,75,147,4,51,127,123,74,239,99,45,217,184,103,13,54,148,126,252,80,147,167,13,3,83,131,63,128,2,33,0,136,195,28,197,208,172,236,4,44,102,112,89,117,71,185,113,111,37,67,156,234,173,255,143,246,176,237,141,88,98,29,122,0,117,0,18,241,78,52,189,83,114,76,132,6,25,195,143,63,122,19,248,231,181,98,135,136,156,109,48,5,132,235,229,134,38,58,0,0,1,152,45,140,236,158,0,0,4,3,0,70,48,68,2,32,81,78,14,41,157,76,141,23,134,250,133,130,103,51,75,237,150,33,80,56,5,61,189,199,190,229,27,255,139,90,159,152,2,32,4,29,66,139,108,115,254,210,107,109,245,70,72,81,160,166,223,49,136,52,77,39,126,136,191,145,229,102,156,27,136,85,48,10,6,8,42,134,72,206,61,4,3,2,3,73,0,48,70,2,33,0,155,180,227,173,100,123,239,205,90,249,188,164,148,101,108,13,241,27,216,13,210,181,6,87,145,117,56,238,206,54,223,66,2,33,0,239,159,142,70,124,211,172,47,136,33,5,79,21,178,150,177,140,243,186,185,123,255,113,47,245,252,196,97,121,100,100,209,164,2,4,0,166,18,4,16,97,112,105,46,100,101,101,112,115,101,101,107,46,99,111,109,169,5,2,3,0,253,31,170,129,211,4,129,208,3,183,109,0,145,198,192,109,21,173,177,169,106,71,158,230,62,16,57,182,211,143,241,157,202,156,17,231,61,172,220,37,130,25,217,42,61,101,115,222,65,31,182,202,150,247,81,191,12,195,209,245,41,109,205,88,217,123,69,28,225,122,162,126,70,12,158,79,118,196,145,146,195,238,144,46,96,180,97,66,187,239,200,134,138,129,229,168,66,80,173,135,61,77,144,184,213,158,141,142,23,97,83,103,83,95,47,43,44,5,244,132,146,176,168,13,3,235,131,3,169,131,104,233,163,53,162,128,110,175,91,175,188,70,117,142,5,17,245,143,213,202,150,169,159,52,39,129,32,203,207,19,162,104,249,58,156,199,134,48,119,66,147,43,18,248,140,248,170,133,35,162,63,153,119,100,55,110,9,62,92,99,118,12,19,4,248,222,214,255,164,137,165,82,234,167,98,91,177,237,47,139,164,249,170,198,86,61,174,7,2,5,0,229,0,215,26,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"api.deepseek.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/v1/chat/completions","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Sun, 27 Jul 2025 15:36:33 GMT","Content-Type","application/json","Content-Length","111","Connection","keep-alive","vary","origin, access-control-request-method, access-control-request-headers","access-control-allow-credentials","true","x-ds-trace-id","3ab4e77101035ce9433a776daa3ee9e8","Strict-Transport-Security","max-age=31536000; includeSubDomains; preload","X-Content-Type-Options","nosniff","cf-cache-status","DYNAMIC","Set-Cookie","__cf_bm=sY7ZsXt99nlvOWvTc8b5D3YYllU5Gx0Z_NQ2HXS1Duk-1753630593-*******-soBVLDslvhENYyTG2FMrnhx1WLjdPiiTtzFKGjzpAwx7rOX2ycPlLSszo93H.Fr6cy6576n1cttxDR3BizhuXGW1Hai59HbE_L.B4OfPLPk; path=/; expires=Sun, 27-Jul-25 16:06:33 GMT; domain=.deepseek.com; HttpOnly; Secure; SameSite=None","Server","cloudflare","CF-RAY","965d368659f9bd42-LHR"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://api.deepseek.com/v1/chat/completions","socket":null,"statusCode":402,"statusMessage":"Payment Required","upgrade":false,"url":""},"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"response":{"config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":"{\"model\":\"deepseek-chat\",\"messages\":[{\"role\":\"system\",\"content\":\"Tu es l'assistant virtuel intelligent de FOCEP SA (Fonds Camerounais d'Épargne et de Crédit pour la Production), une institution de microfinance camerounaise de référence.\\n\\nINFORMATIONS IMPORTANTES SUR FOCEP SA :\\n- Fondée en 2007, 18 ans d'expérience\\n- 250 employés, 15 agences\\n- Plus de 50 000 clients satisfaits\\n- Capital : 1.5 milliard FCFA\\n- Classement : Parmi les 10 meilleurs EMF du Cameroun\\n- Objectif 2027 : Être dans le TOP 5 des meilleurs EMF au Cameroun\\n- Leadership : PCA Eugène Nzouadjeu Yota, DG Bertin Tonleu\\n- Partenaire stratégique : Finafrica depuis 2019\\n\\nCONTACT :\\n- Téléphone : +237 695 021 141\\n- Email : <EMAIL>\\n- Site web : www.focep.cm\\n- Siège : Yaoundé, Cameroun\\n\\nINSTRUCTIONS DE COMPORTEMENT :\\n1. Sois toujours poli, professionnel et empathique\\n2. Utilise un langage chaleureux et personnalisé\\n3. Fournis des informations précises et factuelles uniquement\\n4. Si tu ne connais pas une information, oriente vers un agent humain\\n5. Utilise des émojis appropriés pour rendre les réponses attrayantes\\n6. Adapte ton niveau de langage au contexte bancaire/financier\\n7. Reste dans le contexte de FOCEP SA - ne réponds pas aux questions hors sujet\\n8. Encourage toujours l'engagement client et la satisfaction\\n\\nPRODUITS ET SERVICES DISPONIBLES :\\n- Compte d'épargne: Compte à vue qui permet à son titulaire physique ou morale (associations, GIC, GIE) de sécuriser son...\\n- Compte courant et compte de dépôt simple: Comptes destinés aux commerçants et particuliers sollicitant bénéficier des services bancaires pour ...\\n- Bons de caisse: C'est l'opportunité de placement de fonds offerte par FOCEP à sa clientèle. Ce produit permet aux cl...\\n- Crédits aux jeunes entrepreneurs: Les crédits aux jeunes entrepreneurs pour le financement des activités agro-pastorales et des chaîne...\\n- Crédits aux commerçants et assimilés: Crédits pour le renforcement du fonds de roulement des commerçants et assimilés...\\n- Crédits aux fonctionnaires et employés d'entreprises: Crédits pour le financement des besoins des fonctionnaires et employés d'entreprises domiciliées et ...\\n- Crédit scolaire: Crédit spécialement conçu pour financer les frais de scolarité et fournitures scolaires...\\n- Crédit agricole saisonnier: Financement des activités agricoles selon les saisons de culture...\\n\\nAGENCES PRINCIPALES :\\n- FOCEP Yaoundé Centre (Yaoundé): +237 222 23 14 83\\n- FOCEP Mokolo (Yaoundé): +237 222 23 14 84\\n- FOCEP Douala Ndokoti (Douala): +237 233 42 15 67\\n- FOCEP Douala Akwa (Douala): +237 233 42 15 68\\n- FOCEP Douala Mboppi (Douala): +237 233 42 15 69\\n\\nAnalyse ce message et retourne UNIQUEMENT un objet JSON avec cette structure exacte :\\n{\\n  \\\"intent\\\": \\\"intention_detectee\\\",\\n  \\\"confidence\\\": 0.95,\\n  \\\"language\\\": \\\"fr\\\",\\n  \\\"entities\\\": [{\\\"entity\\\": \\\"type\\\", \\\"value\\\": \\\"valeur\\\", \\\"confidence\\\": 0.9}],\\n  \\\"sentiment\\\": \\\"positive|negative|neutral\\\",\\n  \\\"sentimentScore\\\": 0.5,\\n  \\\"context\\\": \\\"contexte_conversationnel\\\",\\n  \\\"needsHumanAgent\\\": false\\n}\"},{\"role\":\"user\",\"content\":\"Bonjour, je voudrais des informations sur vos crédits\"}],\"max_tokens\":500,\"temperature\":0.3,\"stream\":false}","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3291","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"https://api.deepseek.com/v1/chat/completions","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"data":{"error":{"code":"invalid_request_error","message":"Insufficient Balance","param":null,"type":"unknown_error"}},"headers":{"access-control-allow-credentials":"true","cf-cache-status":"DYNAMIC","cf-ray":"965d368659f9bd42-LHR","connection":"keep-alive","content-length":"111","content-type":"application/json","date":"Sun, 27 Jul 2025 15:36:33 GMT","server":"cloudflare","set-cookie":"__cf_bm=sY7ZsXt99nlvOWvTc8b5D3YYllU5Gx0Z_NQ2HXS1Duk-1753630593-*******-soBVLDslvhENYyTG2FMrnhx1WLjdPiiTtzFKGjzpAwx7rOX2ycPlLSszo93H.Fr6cy6576n1cttxDR3BizhuXGW1Hai59HbE_L.B4OfPLPk; path=/; expires=Sun, 27-Jul-25 16:06:33 GMT; domain=.deepseek.com; HttpOnly; Secure; SameSite=None","strict-transport-security":"max-age=31536000; includeSubDomains; preload","vary":"origin, access-control-request-method, access-control-request-headers","x-content-type-options":"nosniff","x-ds-trace-id":"3ab4e77101035ce9433a776daa3ee9e8"},"request":{"_closed":true,"_contentLength":3291,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer sk-faed2ca2fb944a91a52266d4db96110c\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 3291\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: api.deepseek.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":false,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://api.deepseek.com/v1/chat/completions","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3291","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"hostname":"api.deepseek.com","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["api.deepseek.com:443:::::::::::::::::::::"],"map":{"api.deepseek.com:443:::::::::::::::::::::":{"data":[48,130,5,30,2,1,1,2,2,3,4,4,2,19,2,4,32,182,170,68,151,59,40,242,9,118,166,208,53,152,221,97,83,144,207,51,135,16,80,0,146,191,53,236,54,94,192,246,212,4,48,173,49,217,13,13,145,113,223,195,248,2,96,2,56,236,6,195,102,135,93,130,106,143,132,197,14,119,73,87,213,90,247,76,92,143,52,44,150,228,173,64,177,185,74,66,133,31,135,161,6,2,4,104,134,71,129,162,4,2,2,28,32,163,130,3,164,48,130,3,160,48,130,3,69,160,3,2,1,2,2,17,0,163,190,105,37,120,24,205,248,14,170,212,88,243,140,138,57,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,50,49,49,52,49,52,50,54,90,23,13,50,53,49,48,49,57,49,53,49,52,50,53,90,48,27,49,25,48,23,6,3,85,4,3,19,16,97,112,105,46,100,101,101,112,115,101,101,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,51,118,226,200,6,188,68,18,176,52,138,127,29,17,87,125,247,7,64,158,97,41,227,140,204,191,154,42,152,60,238,73,48,241,141,115,242,12,40,255,141,164,140,169,43,3,11,207,148,218,43,142,115,41,64,112,236,215,88,92,202,202,116,49,163,130,2,72,48,130,2,68,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,76,172,145,127,138,85,102,65,47,11,145,32,46,99,30,234,20,147,94,209,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,111,55,52,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,27,6,3,85,29,17,4,20,48,18,130,16,97,112,105,46,100,101,101,112,115,101,101,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,90,107,106,122,85,88,75,67,85,119,85,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,13,225,242,48,43,211,13,193,64,98,18,9,234,85,46,252,71,116,124,177,215,233,48,239,14,66,30,180,126,78,170,52,0,0,1,152,45,140,236,168,0,0,4,3,0,71,48,69,2,32,72,79,96,141,144,75,147,4,51,127,123,74,239,99,45,217,184,103,13,54,148,126,252,80,147,167,13,3,83,131,63,128,2,33,0,136,195,28,197,208,172,236,4,44,102,112,89,117,71,185,113,111,37,67,156,234,173,255,143,246,176,237,141,88,98,29,122,0,117,0,18,241,78,52,189,83,114,76,132,6,25,195,143,63,122,19,248,231,181,98,135,136,156,109,48,5,132,235,229,134,38,58,0,0,1,152,45,140,236,158,0,0,4,3,0,70,48,68,2,32,81,78,14,41,157,76,141,23,134,250,133,130,103,51,75,237,150,33,80,56,5,61,189,199,190,229,27,255,139,90,159,152,2,32,4,29,66,139,108,115,254,210,107,109,245,70,72,81,160,166,223,49,136,52,77,39,126,136,191,145,229,102,156,27,136,85,48,10,6,8,42,134,72,206,61,4,3,2,3,73,0,48,70,2,33,0,155,180,227,173,100,123,239,205,90,249,188,164,148,101,108,13,241,27,216,13,210,181,6,87,145,117,56,238,206,54,223,66,2,33,0,239,159,142,70,124,211,172,47,136,33,5,79,21,178,150,177,140,243,186,185,123,255,113,47,245,252,196,97,121,100,100,209,164,2,4,0,166,18,4,16,97,112,105,46,100,101,101,112,115,101,101,107,46,99,111,109,169,5,2,3,0,253,31,170,129,211,4,129,208,3,183,109,0,145,198,192,109,21,173,177,169,106,71,158,230,62,16,57,182,211,143,241,157,202,156,17,231,61,172,220,37,130,25,217,42,61,101,115,222,65,31,182,202,150,247,81,191,12,195,209,245,41,109,205,88,217,123,69,28,225,122,162,126,70,12,158,79,118,196,145,146,195,238,144,46,96,180,97,66,187,239,200,134,138,129,229,168,66,80,173,135,61,77,144,184,213,158,141,142,23,97,83,103,83,95,47,43,44,5,244,132,146,176,168,13,3,235,131,3,169,131,104,233,163,53,162,128,110,175,91,175,188,70,117,142,5,17,245,143,213,202,150,169,159,52,39,129,32,203,207,19,162,104,249,58,156,199,134,48,119,66,147,43,18,248,140,248,170,133,35,162,63,153,119,100,55,110,9,62,92,99,118,12,19,4,248,222,214,255,164,137,165,82,234,167,98,91,177,237,47,139,164,249,170,198,86,61,174,7,2,5,0,229,0,215,26,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/v1/chat/completions","pathname":"/v1/chat/completions","port":"","protocol":"https:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":3291,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["api.deepseek.com:443:::::::::::::::::::::"],"map":{"api.deepseek.com:443:::::::::::::::::::::":{"data":[48,130,5,30,2,1,1,2,2,3,4,4,2,19,2,4,32,182,170,68,151,59,40,242,9,118,166,208,53,152,221,97,83,144,207,51,135,16,80,0,146,191,53,236,54,94,192,246,212,4,48,173,49,217,13,13,145,113,223,195,248,2,96,2,56,236,6,195,102,135,93,130,106,143,132,197,14,119,73,87,213,90,247,76,92,143,52,44,150,228,173,64,177,185,74,66,133,31,135,161,6,2,4,104,134,71,129,162,4,2,2,28,32,163,130,3,164,48,130,3,160,48,130,3,69,160,3,2,1,2,2,17,0,163,190,105,37,120,24,205,248,14,170,212,88,243,140,138,57,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,50,49,49,52,49,52,50,54,90,23,13,50,53,49,48,49,57,49,53,49,52,50,53,90,48,27,49,25,48,23,6,3,85,4,3,19,16,97,112,105,46,100,101,101,112,115,101,101,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,51,118,226,200,6,188,68,18,176,52,138,127,29,17,87,125,247,7,64,158,97,41,227,140,204,191,154,42,152,60,238,73,48,241,141,115,242,12,40,255,141,164,140,169,43,3,11,207,148,218,43,142,115,41,64,112,236,215,88,92,202,202,116,49,163,130,2,72,48,130,2,68,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,76,172,145,127,138,85,102,65,47,11,145,32,46,99,30,234,20,147,94,209,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,111,55,52,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,27,6,3,85,29,17,4,20,48,18,130,16,97,112,105,46,100,101,101,112,115,101,101,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,90,107,106,122,85,88,75,67,85,119,85,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,13,225,242,48,43,211,13,193,64,98,18,9,234,85,46,252,71,116,124,177,215,233,48,239,14,66,30,180,126,78,170,52,0,0,1,152,45,140,236,168,0,0,4,3,0,71,48,69,2,32,72,79,96,141,144,75,147,4,51,127,123,74,239,99,45,217,184,103,13,54,148,126,252,80,147,167,13,3,83,131,63,128,2,33,0,136,195,28,197,208,172,236,4,44,102,112,89,117,71,185,113,111,37,67,156,234,173,255,143,246,176,237,141,88,98,29,122,0,117,0,18,241,78,52,189,83,114,76,132,6,25,195,143,63,122,19,248,231,181,98,135,136,156,109,48,5,132,235,229,134,38,58,0,0,1,152,45,140,236,158,0,0,4,3,0,70,48,68,2,32,81,78,14,41,157,76,141,23,134,250,133,130,103,51,75,237,150,33,80,56,5,61,189,199,190,229,27,255,139,90,159,152,2,32,4,29,66,139,108,115,254,210,107,109,245,70,72,81,160,166,223,49,136,52,77,39,126,136,191,145,229,102,156,27,136,85,48,10,6,8,42,134,72,206,61,4,3,2,3,73,0,48,70,2,33,0,155,180,227,173,100,123,239,205,90,249,188,164,148,101,108,13,241,27,216,13,210,181,6,87,145,117,56,238,206,54,223,66,2,33,0,239,159,142,70,124,211,172,47,136,33,5,79,21,178,150,177,140,243,186,185,123,255,113,47,245,252,196,97,121,100,100,209,164,2,4,0,166,18,4,16,97,112,105,46,100,101,101,112,115,101,101,107,46,99,111,109,169,5,2,3,0,253,31,170,129,211,4,129,208,3,183,109,0,145,198,192,109,21,173,177,169,106,71,158,230,62,16,57,182,211,143,241,157,202,156,17,231,61,172,220,37,130,25,217,42,61,101,115,222,65,31,182,202,150,247,81,191,12,195,209,245,41,109,205,88,217,123,69,28,225,122,162,126,70,12,158,79,118,196,145,146,195,238,144,46,96,180,97,66,187,239,200,134,138,129,229,168,66,80,173,135,61,77,144,184,213,158,141,142,23,97,83,103,83,95,47,43,44,5,244,132,146,176,168,13,3,235,131,3,169,131,104,233,163,53,162,128,110,175,91,175,188,70,117,142,5,17,245,143,213,202,150,169,159,52,39,129,32,203,207,19,162,104,249,58,156,199,134,48,119,66,147,43,18,248,140,248,170,133,35,162,63,153,119,100,55,110,9,62,92,99,118,12,19,4,248,222,214,255,164,137,165,82,234,167,98,91,177,237,47,139,164,249,170,198,86,61,174,7,2,5,0,229,0,215,26,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"api.deepseek.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/v1/chat/completions","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Sun, 27 Jul 2025 15:36:33 GMT","Content-Type","application/json","Content-Length","111","Connection","keep-alive","vary","origin, access-control-request-method, access-control-request-headers","access-control-allow-credentials","true","x-ds-trace-id","3ab4e77101035ce9433a776daa3ee9e8","Strict-Transport-Security","max-age=31536000; includeSubDomains; preload","X-Content-Type-Options","nosniff","cf-cache-status","DYNAMIC","Set-Cookie","__cf_bm=sY7ZsXt99nlvOWvTc8b5D3YYllU5Gx0Z_NQ2HXS1Duk-1753630593-*******-soBVLDslvhENYyTG2FMrnhx1WLjdPiiTtzFKGjzpAwx7rOX2ycPlLSszo93H.Fr6cy6576n1cttxDR3BizhuXGW1Hai59HbE_L.B4OfPLPk; path=/; expires=Sun, 27-Jul-25 16:06:33 GMT; domain=.deepseek.com; HttpOnly; Secure; SameSite=None","Server","cloudflare","CF-RAY","965d368659f9bd42-LHR"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://api.deepseek.com/v1/chat/completions","socket":null,"statusCode":402,"statusMessage":"Payment Required","upgrade":false,"url":""},"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"status":402,"statusText":"Payment Required"},"service":"focep-chatbot","stack":"AxiosError: Request failed with status code 402\n    at settle (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DeepSeekService.makeAPICall (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\deepSeekService.js:263:22)\n    at async DeepSeekService.analyzeMessage (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\deepSeekService.js:184:24)\n    at async C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:87:24\n    at async DeepSeekIntegrationValidator.test (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:289:22)\n    at async DeepSeekIntegrationValidator.validateServices (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:86:7)\n    at async DeepSeekIntegrationValidator.runValidation (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:34:7)","status":402,"timestamp":"2025-07-27 17:36:33"}
{"code":"ERR_BAD_REQUEST","component":"nlp","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":"{\"model\":\"deepseek-chat\",\"messages\":[{\"role\":\"system\",\"content\":\"Tu es l'assistant virtuel intelligent de FOCEP SA (Fonds Camerounais d'Épargne et de Crédit pour la Production), une institution de microfinance camerounaise de référence.\\n\\nINFORMATIONS IMPORTANTES SUR FOCEP SA :\\n- Fondée en 2007, 18 ans d'expérience\\n- 250 employés, 15 agences\\n- Plus de 50 000 clients satisfaits\\n- Capital : 1.5 milliard FCFA\\n- Classement : Parmi les 10 meilleurs EMF du Cameroun\\n- Objectif 2027 : Être dans le TOP 5 des meilleurs EMF au Cameroun\\n- Leadership : PCA Eugène Nzouadjeu Yota, DG Bertin Tonleu\\n- Partenaire stratégique : Finafrica depuis 2019\\n\\nCONTACT :\\n- Téléphone : +237 695 021 141\\n- Email : <EMAIL>\\n- Site web : www.focep.cm\\n- Siège : Yaoundé, Cameroun\\n\\nINSTRUCTIONS DE COMPORTEMENT :\\n1. Sois toujours poli, professionnel et empathique\\n2. Utilise un langage chaleureux et personnalisé\\n3. Fournis des informations précises et factuelles uniquement\\n4. Si tu ne connais pas une information, oriente vers un agent humain\\n5. Utilise des émojis appropriés pour rendre les réponses attrayantes\\n6. Adapte ton niveau de langage au contexte bancaire/financier\\n7. Reste dans le contexte de FOCEP SA - ne réponds pas aux questions hors sujet\\n8. Encourage toujours l'engagement client et la satisfaction\\n\\nPRODUITS ET SERVICES DISPONIBLES :\\n- Compte d'épargne: Compte à vue qui permet à son titulaire physique ou morale (associations, GIC, GIE) de sécuriser son...\\n- Compte courant et compte de dépôt simple: Comptes destinés aux commerçants et particuliers sollicitant bénéficier des services bancaires pour ...\\n- Bons de caisse: C'est l'opportunité de placement de fonds offerte par FOCEP à sa clientèle. Ce produit permet aux cl...\\n- Crédits aux jeunes entrepreneurs: Les crédits aux jeunes entrepreneurs pour le financement des activités agro-pastorales et des chaîne...\\n- Crédits aux commerçants et assimilés: Crédits pour le renforcement du fonds de roulement des commerçants et assimilés...\\n- Crédits aux fonctionnaires et employés d'entreprises: Crédits pour le financement des besoins des fonctionnaires et employés d'entreprises domiciliées et ...\\n- Crédit scolaire: Crédit spécialement conçu pour financer les frais de scolarité et fournitures scolaires...\\n- Crédit agricole saisonnier: Financement des activités agricoles selon les saisons de culture...\\n\\nAGENCES PRINCIPALES :\\n- FOCEP Yaoundé Centre (Yaoundé): +237 222 23 14 83\\n- FOCEP Mokolo (Yaoundé): +237 222 23 14 84\\n- FOCEP Douala Ndokoti (Douala): +237 233 42 15 67\\n- FOCEP Douala Akwa (Douala): +237 233 42 15 68\\n- FOCEP Douala Mboppi (Douala): +237 233 42 15 69\\n\\nCONTEXTE CLIENT PERSONNALISÉ :\\n\\nINSTRUCTIONS SPÉCIALES :\\n- Utilise ce contexte pour personnaliser ta réponse\\n- Fais référence aux interactions précédentes si pertinent\\n- Adapte ton niveau de détail selon le type de client\\n- Si le client a demandé un agent humain, propose de le mettre en relation\\n- Maintiens un ton cohérent avec le niveau de satisfaction détecté\\n- Génère une réponse naturelle, empathique et professionnelle\\n- Utilise des émojis appropriés pour rendre la réponse attrayante\\n- Reste factuel et ne invente jamais d'informations sur FOCEP SA\"},{\"role\":\"user\",\"content\":\"Bonjour\"}],\"max_tokens\":1000,\"temperature\":0.7,\"stream\":false}","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3409","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"https://api.deepseek.com/v1/chat/completions","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"level":"error","message":"Erreur lors de la génération de réponse DeepSeek: Request failed with status code 402","name":"AxiosError","request":{"_closed":true,"_contentLength":3409,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer sk-faed2ca2fb944a91a52266d4db96110c\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 3409\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: api.deepseek.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":false,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://api.deepseek.com/v1/chat/completions","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3409","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"hostname":"api.deepseek.com","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["api.deepseek.com:443:::::::::::::::::::::"],"map":{"api.deepseek.com:443:::::::::::::::::::::":{"data":[48,130,5,30,2,1,1,2,2,3,4,4,2,19,2,4,32,182,170,68,151,59,40,242,9,118,166,208,53,152,221,97,83,144,207,51,135,16,80,0,146,191,53,236,54,94,192,246,212,4,48,173,49,217,13,13,145,113,223,195,248,2,96,2,56,236,6,195,102,135,93,130,106,143,132,197,14,119,73,87,213,90,247,76,92,143,52,44,150,228,173,64,177,185,74,66,133,31,135,161,6,2,4,104,134,71,129,162,4,2,2,28,32,163,130,3,164,48,130,3,160,48,130,3,69,160,3,2,1,2,2,17,0,163,190,105,37,120,24,205,248,14,170,212,88,243,140,138,57,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,50,49,49,52,49,52,50,54,90,23,13,50,53,49,48,49,57,49,53,49,52,50,53,90,48,27,49,25,48,23,6,3,85,4,3,19,16,97,112,105,46,100,101,101,112,115,101,101,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,51,118,226,200,6,188,68,18,176,52,138,127,29,17,87,125,247,7,64,158,97,41,227,140,204,191,154,42,152,60,238,73,48,241,141,115,242,12,40,255,141,164,140,169,43,3,11,207,148,218,43,142,115,41,64,112,236,215,88,92,202,202,116,49,163,130,2,72,48,130,2,68,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,76,172,145,127,138,85,102,65,47,11,145,32,46,99,30,234,20,147,94,209,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,111,55,52,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,27,6,3,85,29,17,4,20,48,18,130,16,97,112,105,46,100,101,101,112,115,101,101,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,90,107,106,122,85,88,75,67,85,119,85,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,13,225,242,48,43,211,13,193,64,98,18,9,234,85,46,252,71,116,124,177,215,233,48,239,14,66,30,180,126,78,170,52,0,0,1,152,45,140,236,168,0,0,4,3,0,71,48,69,2,32,72,79,96,141,144,75,147,4,51,127,123,74,239,99,45,217,184,103,13,54,148,126,252,80,147,167,13,3,83,131,63,128,2,33,0,136,195,28,197,208,172,236,4,44,102,112,89,117,71,185,113,111,37,67,156,234,173,255,143,246,176,237,141,88,98,29,122,0,117,0,18,241,78,52,189,83,114,76,132,6,25,195,143,63,122,19,248,231,181,98,135,136,156,109,48,5,132,235,229,134,38,58,0,0,1,152,45,140,236,158,0,0,4,3,0,70,48,68,2,32,81,78,14,41,157,76,141,23,134,250,133,130,103,51,75,237,150,33,80,56,5,61,189,199,190,229,27,255,139,90,159,152,2,32,4,29,66,139,108,115,254,210,107,109,245,70,72,81,160,166,223,49,136,52,77,39,126,136,191,145,229,102,156,27,136,85,48,10,6,8,42,134,72,206,61,4,3,2,3,73,0,48,70,2,33,0,155,180,227,173,100,123,239,205,90,249,188,164,148,101,108,13,241,27,216,13,210,181,6,87,145,117,56,238,206,54,223,66,2,33,0,239,159,142,70,124,211,172,47,136,33,5,79,21,178,150,177,140,243,186,185,123,255,113,47,245,252,196,97,121,100,100,209,164,2,4,0,166,18,4,16,97,112,105,46,100,101,101,112,115,101,101,107,46,99,111,109,169,5,2,3,0,253,31,170,129,211,4,129,208,3,183,109,0,145,198,192,109,21,173,177,169,106,71,158,230,62,16,57,182,211,143,241,157,202,156,17,231,61,172,220,37,130,25,217,42,61,101,115,222,65,31,182,202,150,247,81,191,12,195,209,245,41,109,205,88,217,123,69,28,225,122,162,126,70,12,158,79,118,196,145,146,195,238,144,46,96,180,97,66,187,239,200,134,138,129,229,168,66,80,173,135,61,77,144,184,213,158,141,142,23,97,83,103,83,95,47,43,44,5,244,132,146,176,168,13,3,235,131,3,169,131,104,233,163,53,162,128,110,175,91,175,188,70,117,142,5,17,245,143,213,202,150,169,159,52,39,129,32,203,207,19,162,104,249,58,156,199,134,48,119,66,147,43,18,248,140,248,170,133,35,162,63,153,119,100,55,110,9,62,92,99,118,12,19,4,248,222,214,255,164,137,165,82,234,167,98,91,177,237,47,139,164,249,170,198,86,61,174,7,2,5,0,229,0,215,26,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/v1/chat/completions","pathname":"/v1/chat/completions","port":"","protocol":"https:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":3409,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["api.deepseek.com:443:::::::::::::::::::::"],"map":{"api.deepseek.com:443:::::::::::::::::::::":{"data":[48,130,5,30,2,1,1,2,2,3,4,4,2,19,2,4,32,182,170,68,151,59,40,242,9,118,166,208,53,152,221,97,83,144,207,51,135,16,80,0,146,191,53,236,54,94,192,246,212,4,48,173,49,217,13,13,145,113,223,195,248,2,96,2,56,236,6,195,102,135,93,130,106,143,132,197,14,119,73,87,213,90,247,76,92,143,52,44,150,228,173,64,177,185,74,66,133,31,135,161,6,2,4,104,134,71,129,162,4,2,2,28,32,163,130,3,164,48,130,3,160,48,130,3,69,160,3,2,1,2,2,17,0,163,190,105,37,120,24,205,248,14,170,212,88,243,140,138,57,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,50,49,49,52,49,52,50,54,90,23,13,50,53,49,48,49,57,49,53,49,52,50,53,90,48,27,49,25,48,23,6,3,85,4,3,19,16,97,112,105,46,100,101,101,112,115,101,101,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,51,118,226,200,6,188,68,18,176,52,138,127,29,17,87,125,247,7,64,158,97,41,227,140,204,191,154,42,152,60,238,73,48,241,141,115,242,12,40,255,141,164,140,169,43,3,11,207,148,218,43,142,115,41,64,112,236,215,88,92,202,202,116,49,163,130,2,72,48,130,2,68,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,76,172,145,127,138,85,102,65,47,11,145,32,46,99,30,234,20,147,94,209,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,111,55,52,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,27,6,3,85,29,17,4,20,48,18,130,16,97,112,105,46,100,101,101,112,115,101,101,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,90,107,106,122,85,88,75,67,85,119,85,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,13,225,242,48,43,211,13,193,64,98,18,9,234,85,46,252,71,116,124,177,215,233,48,239,14,66,30,180,126,78,170,52,0,0,1,152,45,140,236,168,0,0,4,3,0,71,48,69,2,32,72,79,96,141,144,75,147,4,51,127,123,74,239,99,45,217,184,103,13,54,148,126,252,80,147,167,13,3,83,131,63,128,2,33,0,136,195,28,197,208,172,236,4,44,102,112,89,117,71,185,113,111,37,67,156,234,173,255,143,246,176,237,141,88,98,29,122,0,117,0,18,241,78,52,189,83,114,76,132,6,25,195,143,63,122,19,248,231,181,98,135,136,156,109,48,5,132,235,229,134,38,58,0,0,1,152,45,140,236,158,0,0,4,3,0,70,48,68,2,32,81,78,14,41,157,76,141,23,134,250,133,130,103,51,75,237,150,33,80,56,5,61,189,199,190,229,27,255,139,90,159,152,2,32,4,29,66,139,108,115,254,210,107,109,245,70,72,81,160,166,223,49,136,52,77,39,126,136,191,145,229,102,156,27,136,85,48,10,6,8,42,134,72,206,61,4,3,2,3,73,0,48,70,2,33,0,155,180,227,173,100,123,239,205,90,249,188,164,148,101,108,13,241,27,216,13,210,181,6,87,145,117,56,238,206,54,223,66,2,33,0,239,159,142,70,124,211,172,47,136,33,5,79,21,178,150,177,140,243,186,185,123,255,113,47,245,252,196,97,121,100,100,209,164,2,4,0,166,18,4,16,97,112,105,46,100,101,101,112,115,101,101,107,46,99,111,109,169,5,2,3,0,253,31,170,129,211,4,129,208,3,183,109,0,145,198,192,109,21,173,177,169,106,71,158,230,62,16,57,182,211,143,241,157,202,156,17,231,61,172,220,37,130,25,217,42,61,101,115,222,65,31,182,202,150,247,81,191,12,195,209,245,41,109,205,88,217,123,69,28,225,122,162,126,70,12,158,79,118,196,145,146,195,238,144,46,96,180,97,66,187,239,200,134,138,129,229,168,66,80,173,135,61,77,144,184,213,158,141,142,23,97,83,103,83,95,47,43,44,5,244,132,146,176,168,13,3,235,131,3,169,131,104,233,163,53,162,128,110,175,91,175,188,70,117,142,5,17,245,143,213,202,150,169,159,52,39,129,32,203,207,19,162,104,249,58,156,199,134,48,119,66,147,43,18,248,140,248,170,133,35,162,63,153,119,100,55,110,9,62,92,99,118,12,19,4,248,222,214,255,164,137,165,82,234,167,98,91,177,237,47,139,164,249,170,198,86,61,174,7,2,5,0,229,0,215,26,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"api.deepseek.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/v1/chat/completions","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Sun, 27 Jul 2025 15:36:35 GMT","Content-Type","application/json","Content-Length","111","Connection","keep-alive","vary","origin, access-control-request-method, access-control-request-headers","access-control-allow-credentials","true","x-ds-trace-id","004574f0bd0e5d8762854bed174b7fd8","Strict-Transport-Security","max-age=31536000; includeSubDomains; preload","X-Content-Type-Options","nosniff","cf-cache-status","DYNAMIC","Set-Cookie","__cf_bm=9V7S6759gs4sujiEyWVp2gc45QdGuf5jM_DTcv54WqY-1753630595-*******-2Yz5Y9XM0DgWN.Xd_DyLwyVfgQnnb8CFnX1puX0kDHFWdUyy61usuV_Yogpr_jQ1lQqsm5vmVaSGhda2CuUBYjwqXAkhi.QXZt.9aedojJY; path=/; expires=Sun, 27-Jul-25 16:06:35 GMT; domain=.deepseek.com; HttpOnly; Secure; SameSite=None","Server","cloudflare","CF-RAY","965d36934eafbd42-LHR"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://api.deepseek.com/v1/chat/completions","socket":null,"statusCode":402,"statusMessage":"Payment Required","upgrade":false,"url":""},"reusedSocket":true,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"response":{"config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":"{\"model\":\"deepseek-chat\",\"messages\":[{\"role\":\"system\",\"content\":\"Tu es l'assistant virtuel intelligent de FOCEP SA (Fonds Camerounais d'Épargne et de Crédit pour la Production), une institution de microfinance camerounaise de référence.\\n\\nINFORMATIONS IMPORTANTES SUR FOCEP SA :\\n- Fondée en 2007, 18 ans d'expérience\\n- 250 employés, 15 agences\\n- Plus de 50 000 clients satisfaits\\n- Capital : 1.5 milliard FCFA\\n- Classement : Parmi les 10 meilleurs EMF du Cameroun\\n- Objectif 2027 : Être dans le TOP 5 des meilleurs EMF au Cameroun\\n- Leadership : PCA Eugène Nzouadjeu Yota, DG Bertin Tonleu\\n- Partenaire stratégique : Finafrica depuis 2019\\n\\nCONTACT :\\n- Téléphone : +237 695 021 141\\n- Email : <EMAIL>\\n- Site web : www.focep.cm\\n- Siège : Yaoundé, Cameroun\\n\\nINSTRUCTIONS DE COMPORTEMENT :\\n1. Sois toujours poli, professionnel et empathique\\n2. Utilise un langage chaleureux et personnalisé\\n3. Fournis des informations précises et factuelles uniquement\\n4. Si tu ne connais pas une information, oriente vers un agent humain\\n5. Utilise des émojis appropriés pour rendre les réponses attrayantes\\n6. Adapte ton niveau de langage au contexte bancaire/financier\\n7. Reste dans le contexte de FOCEP SA - ne réponds pas aux questions hors sujet\\n8. Encourage toujours l'engagement client et la satisfaction\\n\\nPRODUITS ET SERVICES DISPONIBLES :\\n- Compte d'épargne: Compte à vue qui permet à son titulaire physique ou morale (associations, GIC, GIE) de sécuriser son...\\n- Compte courant et compte de dépôt simple: Comptes destinés aux commerçants et particuliers sollicitant bénéficier des services bancaires pour ...\\n- Bons de caisse: C'est l'opportunité de placement de fonds offerte par FOCEP à sa clientèle. Ce produit permet aux cl...\\n- Crédits aux jeunes entrepreneurs: Les crédits aux jeunes entrepreneurs pour le financement des activités agro-pastorales et des chaîne...\\n- Crédits aux commerçants et assimilés: Crédits pour le renforcement du fonds de roulement des commerçants et assimilés...\\n- Crédits aux fonctionnaires et employés d'entreprises: Crédits pour le financement des besoins des fonctionnaires et employés d'entreprises domiciliées et ...\\n- Crédit scolaire: Crédit spécialement conçu pour financer les frais de scolarité et fournitures scolaires...\\n- Crédit agricole saisonnier: Financement des activités agricoles selon les saisons de culture...\\n\\nAGENCES PRINCIPALES :\\n- FOCEP Yaoundé Centre (Yaoundé): +237 222 23 14 83\\n- FOCEP Mokolo (Yaoundé): +237 222 23 14 84\\n- FOCEP Douala Ndokoti (Douala): +237 233 42 15 67\\n- FOCEP Douala Akwa (Douala): +237 233 42 15 68\\n- FOCEP Douala Mboppi (Douala): +237 233 42 15 69\\n\\nCONTEXTE CLIENT PERSONNALISÉ :\\n\\nINSTRUCTIONS SPÉCIALES :\\n- Utilise ce contexte pour personnaliser ta réponse\\n- Fais référence aux interactions précédentes si pertinent\\n- Adapte ton niveau de détail selon le type de client\\n- Si le client a demandé un agent humain, propose de le mettre en relation\\n- Maintiens un ton cohérent avec le niveau de satisfaction détecté\\n- Génère une réponse naturelle, empathique et professionnelle\\n- Utilise des émojis appropriés pour rendre la réponse attrayante\\n- Reste factuel et ne invente jamais d'informations sur FOCEP SA\"},{\"role\":\"user\",\"content\":\"Bonjour\"}],\"max_tokens\":1000,\"temperature\":0.7,\"stream\":false}","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3409","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"https://api.deepseek.com/v1/chat/completions","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"data":{"error":{"code":"invalid_request_error","message":"Insufficient Balance","param":null,"type":"unknown_error"}},"headers":{"access-control-allow-credentials":"true","cf-cache-status":"DYNAMIC","cf-ray":"965d36934eafbd42-LHR","connection":"keep-alive","content-length":"111","content-type":"application/json","date":"Sun, 27 Jul 2025 15:36:35 GMT","server":"cloudflare","set-cookie":"__cf_bm=9V7S6759gs4sujiEyWVp2gc45QdGuf5jM_DTcv54WqY-1753630595-*******-2Yz5Y9XM0DgWN.Xd_DyLwyVfgQnnb8CFnX1puX0kDHFWdUyy61usuV_Yogpr_jQ1lQqsm5vmVaSGhda2CuUBYjwqXAkhi.QXZt.9aedojJY; path=/; expires=Sun, 27-Jul-25 16:06:35 GMT; domain=.deepseek.com; HttpOnly; Secure; SameSite=None","strict-transport-security":"max-age=31536000; includeSubDomains; preload","vary":"origin, access-control-request-method, access-control-request-headers","x-content-type-options":"nosniff","x-ds-trace-id":"004574f0bd0e5d8762854bed174b7fd8"},"request":{"_closed":true,"_contentLength":3409,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer sk-faed2ca2fb944a91a52266d4db96110c\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 3409\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: api.deepseek.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":false,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://api.deepseek.com/v1/chat/completions","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer sk-faed2ca2fb944a91a52266d4db96110c","Content-Length":"3409","Content-Type":"application/json","User-Agent":"axios/1.11.0"},"hostname":"api.deepseek.com","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["api.deepseek.com:443:::::::::::::::::::::"],"map":{"api.deepseek.com:443:::::::::::::::::::::":{"data":[48,130,5,30,2,1,1,2,2,3,4,4,2,19,2,4,32,182,170,68,151,59,40,242,9,118,166,208,53,152,221,97,83,144,207,51,135,16,80,0,146,191,53,236,54,94,192,246,212,4,48,173,49,217,13,13,145,113,223,195,248,2,96,2,56,236,6,195,102,135,93,130,106,143,132,197,14,119,73,87,213,90,247,76,92,143,52,44,150,228,173,64,177,185,74,66,133,31,135,161,6,2,4,104,134,71,129,162,4,2,2,28,32,163,130,3,164,48,130,3,160,48,130,3,69,160,3,2,1,2,2,17,0,163,190,105,37,120,24,205,248,14,170,212,88,243,140,138,57,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,50,49,49,52,49,52,50,54,90,23,13,50,53,49,48,49,57,49,53,49,52,50,53,90,48,27,49,25,48,23,6,3,85,4,3,19,16,97,112,105,46,100,101,101,112,115,101,101,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,51,118,226,200,6,188,68,18,176,52,138,127,29,17,87,125,247,7,64,158,97,41,227,140,204,191,154,42,152,60,238,73,48,241,141,115,242,12,40,255,141,164,140,169,43,3,11,207,148,218,43,142,115,41,64,112,236,215,88,92,202,202,116,49,163,130,2,72,48,130,2,68,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,76,172,145,127,138,85,102,65,47,11,145,32,46,99,30,234,20,147,94,209,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,111,55,52,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,27,6,3,85,29,17,4,20,48,18,130,16,97,112,105,46,100,101,101,112,115,101,101,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,90,107,106,122,85,88,75,67,85,119,85,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,13,225,242,48,43,211,13,193,64,98,18,9,234,85,46,252,71,116,124,177,215,233,48,239,14,66,30,180,126,78,170,52,0,0,1,152,45,140,236,168,0,0,4,3,0,71,48,69,2,32,72,79,96,141,144,75,147,4,51,127,123,74,239,99,45,217,184,103,13,54,148,126,252,80,147,167,13,3,83,131,63,128,2,33,0,136,195,28,197,208,172,236,4,44,102,112,89,117,71,185,113,111,37,67,156,234,173,255,143,246,176,237,141,88,98,29,122,0,117,0,18,241,78,52,189,83,114,76,132,6,25,195,143,63,122,19,248,231,181,98,135,136,156,109,48,5,132,235,229,134,38,58,0,0,1,152,45,140,236,158,0,0,4,3,0,70,48,68,2,32,81,78,14,41,157,76,141,23,134,250,133,130,103,51,75,237,150,33,80,56,5,61,189,199,190,229,27,255,139,90,159,152,2,32,4,29,66,139,108,115,254,210,107,109,245,70,72,81,160,166,223,49,136,52,77,39,126,136,191,145,229,102,156,27,136,85,48,10,6,8,42,134,72,206,61,4,3,2,3,73,0,48,70,2,33,0,155,180,227,173,100,123,239,205,90,249,188,164,148,101,108,13,241,27,216,13,210,181,6,87,145,117,56,238,206,54,223,66,2,33,0,239,159,142,70,124,211,172,47,136,33,5,79,21,178,150,177,140,243,186,185,123,255,113,47,245,252,196,97,121,100,100,209,164,2,4,0,166,18,4,16,97,112,105,46,100,101,101,112,115,101,101,107,46,99,111,109,169,5,2,3,0,253,31,170,129,211,4,129,208,3,183,109,0,145,198,192,109,21,173,177,169,106,71,158,230,62,16,57,182,211,143,241,157,202,156,17,231,61,172,220,37,130,25,217,42,61,101,115,222,65,31,182,202,150,247,81,191,12,195,209,245,41,109,205,88,217,123,69,28,225,122,162,126,70,12,158,79,118,196,145,146,195,238,144,46,96,180,97,66,187,239,200,134,138,129,229,168,66,80,173,135,61,77,144,184,213,158,141,142,23,97,83,103,83,95,47,43,44,5,244,132,146,176,168,13,3,235,131,3,169,131,104,233,163,53,162,128,110,175,91,175,188,70,117,142,5,17,245,143,213,202,150,169,159,52,39,129,32,203,207,19,162,104,249,58,156,199,134,48,119,66,147,43,18,248,140,248,170,133,35,162,63,153,119,100,55,110,9,62,92,99,118,12,19,4,248,222,214,255,164,137,165,82,234,167,98,91,177,237,47,139,164,249,170,198,86,61,174,7,2,5,0,229,0,215,26,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/v1/chat/completions","pathname":"/v1/chat/completions","port":"","protocol":"https:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":3409,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["api.deepseek.com:443:::::::::::::::::::::"],"map":{"api.deepseek.com:443:::::::::::::::::::::":{"data":[48,130,5,30,2,1,1,2,2,3,4,4,2,19,2,4,32,182,170,68,151,59,40,242,9,118,166,208,53,152,221,97,83,144,207,51,135,16,80,0,146,191,53,236,54,94,192,246,212,4,48,173,49,217,13,13,145,113,223,195,248,2,96,2,56,236,6,195,102,135,93,130,106,143,132,197,14,119,73,87,213,90,247,76,92,143,52,44,150,228,173,64,177,185,74,66,133,31,135,161,6,2,4,104,134,71,129,162,4,2,2,28,32,163,130,3,164,48,130,3,160,48,130,3,69,160,3,2,1,2,2,17,0,163,190,105,37,120,24,205,248,14,170,212,88,243,140,138,57,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,50,49,49,52,49,52,50,54,90,23,13,50,53,49,48,49,57,49,53,49,52,50,53,90,48,27,49,25,48,23,6,3,85,4,3,19,16,97,112,105,46,100,101,101,112,115,101,101,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,51,118,226,200,6,188,68,18,176,52,138,127,29,17,87,125,247,7,64,158,97,41,227,140,204,191,154,42,152,60,238,73,48,241,141,115,242,12,40,255,141,164,140,169,43,3,11,207,148,218,43,142,115,41,64,112,236,215,88,92,202,202,116,49,163,130,2,72,48,130,2,68,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,76,172,145,127,138,85,102,65,47,11,145,32,46,99,30,234,20,147,94,209,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,111,55,52,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,27,6,3,85,29,17,4,20,48,18,130,16,97,112,105,46,100,101,101,112,115,101,101,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,90,107,106,122,85,88,75,67,85,119,85,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,13,225,242,48,43,211,13,193,64,98,18,9,234,85,46,252,71,116,124,177,215,233,48,239,14,66,30,180,126,78,170,52,0,0,1,152,45,140,236,168,0,0,4,3,0,71,48,69,2,32,72,79,96,141,144,75,147,4,51,127,123,74,239,99,45,217,184,103,13,54,148,126,252,80,147,167,13,3,83,131,63,128,2,33,0,136,195,28,197,208,172,236,4,44,102,112,89,117,71,185,113,111,37,67,156,234,173,255,143,246,176,237,141,88,98,29,122,0,117,0,18,241,78,52,189,83,114,76,132,6,25,195,143,63,122,19,248,231,181,98,135,136,156,109,48,5,132,235,229,134,38,58,0,0,1,152,45,140,236,158,0,0,4,3,0,70,48,68,2,32,81,78,14,41,157,76,141,23,134,250,133,130,103,51,75,237,150,33,80,56,5,61,189,199,190,229,27,255,139,90,159,152,2,32,4,29,66,139,108,115,254,210,107,109,245,70,72,81,160,166,223,49,136,52,77,39,126,136,191,145,229,102,156,27,136,85,48,10,6,8,42,134,72,206,61,4,3,2,3,73,0,48,70,2,33,0,155,180,227,173,100,123,239,205,90,249,188,164,148,101,108,13,241,27,216,13,210,181,6,87,145,117,56,238,206,54,223,66,2,33,0,239,159,142,70,124,211,172,47,136,33,5,79,21,178,150,177,140,243,186,185,123,255,113,47,245,252,196,97,121,100,100,209,164,2,4,0,166,18,4,16,97,112,105,46,100,101,101,112,115,101,101,107,46,99,111,109,169,5,2,3,0,253,31,170,129,211,4,129,208,3,183,109,0,145,198,192,109,21,173,177,169,106,71,158,230,62,16,57,182,211,143,241,157,202,156,17,231,61,172,220,37,130,25,217,42,61,101,115,222,65,31,182,202,150,247,81,191,12,195,209,245,41,109,205,88,217,123,69,28,225,122,162,126,70,12,158,79,118,196,145,146,195,238,144,46,96,180,97,66,187,239,200,134,138,129,229,168,66,80,173,135,61,77,144,184,213,158,141,142,23,97,83,103,83,95,47,43,44,5,244,132,146,176,168,13,3,235,131,3,169,131,104,233,163,53,162,128,110,175,91,175,188,70,117,142,5,17,245,143,213,202,150,169,159,52,39,129,32,203,207,19,162,104,249,58,156,199,134,48,119,66,147,43,18,248,140,248,170,133,35,162,63,153,119,100,55,110,9,62,92,99,118,12,19,4,248,222,214,255,164,137,165,82,234,167,98,91,177,237,47,139,164,249,170,198,86,61,174,7,2,5,0,229,0,215,26,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"api.deepseek.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"api.deepseek.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/v1/chat/completions","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"api.deepseek.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"api.deepseek.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Sun, 27 Jul 2025 15:36:35 GMT","Content-Type","application/json","Content-Length","111","Connection","keep-alive","vary","origin, access-control-request-method, access-control-request-headers","access-control-allow-credentials","true","x-ds-trace-id","004574f0bd0e5d8762854bed174b7fd8","Strict-Transport-Security","max-age=31536000; includeSubDomains; preload","X-Content-Type-Options","nosniff","cf-cache-status","DYNAMIC","Set-Cookie","__cf_bm=9V7S6759gs4sujiEyWVp2gc45QdGuf5jM_DTcv54WqY-1753630595-*******-2Yz5Y9XM0DgWN.Xd_DyLwyVfgQnnb8CFnX1puX0kDHFWdUyy61usuV_Yogpr_jQ1lQqsm5vmVaSGhda2CuUBYjwqXAkhi.QXZt.9aedojJY; path=/; expires=Sun, 27-Jul-25 16:06:35 GMT; domain=.deepseek.com; HttpOnly; Secure; SameSite=None","Server","cloudflare","CF-RAY","965d36934eafbd42-LHR"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://api.deepseek.com/v1/chat/completions","socket":null,"statusCode":402,"statusMessage":"Payment Required","upgrade":false,"url":""},"reusedSocket":true,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"status":402,"statusText":"Payment Required"},"service":"focep-chatbot","stack":"AxiosError: Request failed with status code 402\n    at settle (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DeepSeekService.makeAPICall (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\deepSeekService.js:263:22)\n    at async DeepSeekService.generateContextualResponse (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\deepSeekService.js:321:24)\n    at async C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:101:24\n    at async DeepSeekIntegrationValidator.test (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:289:22)\n    at async DeepSeekIntegrationValidator.validateServices (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:100:7)\n    at async DeepSeekIntegrationValidator.runValidation (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\validateDeepSeekIntegration.js:34:7)","status":402,"timestamp":"2025-07-27 17:36:35"}
{"context":{"language":"fr"},"level":"error","message":"Réponse d'urgence activée","service":"focep-chatbot","timestamp":"2025-07-27 17:36:35"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 17:36:46"}
{"context":{"language":"fr"},"level":"error","message":"Réponse d'urgence activée","service":"focep-chatbot","timestamp":"2025-07-27 17:41:09"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Operation `autoresponses.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `autoresponses.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-27 17:41:19"}
{"context":{"intent":"greeting","language":"fr","text":"Test message"},"level":"error","message":"Réponse d'urgence activée","service":"focep-chatbot","timestamp":"2025-07-27 17:41:19"}
{"context":{"language":"fr"},"level":"error","message":"Réponse d'urgence activée","service":"focep-chatbot","timestamp":"2025-07-27 18:03:22"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Access denied for user 'root'@'localhost' (using password: YES)","name":"SequelizeAccessDeniedError","original":{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"sqlMessage":"Access denied for user 'root'@'localhost' (using password: YES)","sqlState":"28000"},"parent":{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"sqlMessage":"Access denied for user 'root'@'localhost' (using password: YES)","sqlState":"28000"},"service":"focep-chatbot","stack":"SequelizeAccessDeniedError: Access denied for user 'root'@'localhost' (using password: YES)\n    at ConnectionManager.connect (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\sequelize\\lib\\dialects\\mysql\\connection-manager.js:94:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager._connect (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:222:24)\n    at async C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:174:32\n    at async ConnectionManager.getConnection (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:197:7)\n    at async C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\sequelize\\lib\\sequelize.js:305:26\n    at async MySQLQueryInterface.select (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async AutoResponse.findAll (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async AutoResponse.findOne (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\sequelize\\lib\\model.js:1240:12)\n    at async ResponseGenerator.findAutoResponse (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\responseGenerator.js:252:14)","timestamp":"2025-07-27 18:03:24"}
{"context":{"intent":"greeting","language":"fr","text":"Test message"},"level":"error","message":"Réponse d'urgence activée","service":"focep-chatbot","timestamp":"2025-07-27 18:03:24"}
{"context":{"language":"fr"},"level":"error","message":"Réponse d'urgence activée","service":"focep-chatbot","timestamp":"2025-07-27 18:10:55"}
{"level":"error","message":"Erreur lors de la recherche de réponse auto: Unknown database 'focep_chatbot'","name":"SequelizeConnectionError","original":{"code":"ER_BAD_DB_ERROR","errno":1049,"sqlMessage":"Unknown database 'focep_chatbot'","sqlState":"42000"},"parent":{"code":"ER_BAD_DB_ERROR","errno":1049,"sqlMessage":"Unknown database 'focep_chatbot'","sqlState":"42000"},"service":"focep-chatbot","stack":"SequelizeConnectionError: Unknown database 'focep_chatbot'\n    at ConnectionManager.connect (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\sequelize\\lib\\dialects\\mysql\\connection-manager.js:102:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager._connect (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:222:24)\n    at async C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:174:32\n    at async ConnectionManager.getConnection (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:197:7)\n    at async C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\sequelize\\lib\\sequelize.js:305:26\n    at async MySQLQueryInterface.select (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async AutoResponse.findAll (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async AutoResponse.findOne (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\sequelize\\lib\\model.js:1240:12)\n    at async ResponseGenerator.findAutoResponse (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\responseGenerator.js:252:14)","timestamp":"2025-07-27 18:10:56"}
{"context":{"intent":"greeting","language":"fr","text":"Test message"},"level":"error","message":"Réponse d'urgence activée","service":"focep-chatbot","timestamp":"2025-07-27 18:10:56"}
{"context":{"language":"fr"},"level":"error","message":"Réponse d'urgence activée","service":"focep-chatbot","timestamp":"2025-07-27 18:22:06"}
{"context":{"intent":"greeting","language":"fr","text":"Test message"},"level":"error","message":"Réponse d'urgence activée","service":"focep-chatbot","timestamp":"2025-07-27 18:22:10"}
{"component":"nlp","level":"error","message":"Erreur Groq:","service":"focep-chatbot","timestamp":"2025-07-27 22:16:25"}
{"component":"nlp","level":"error","message":"Erreur scraping Site FOCEP Officiel:","service":"focep-chatbot","timestamp":"2025-07-28 02:23:20"}
{"component":"nlp","level":"error","message":"Erreur scraping social Twitter/X FOCEP:","service":"focep-chatbot","timestamp":"2025-07-28 02:23:38"}
{"component":"nlp","level":"error","message":"Erreur recherche focep sur Cameroon Tribune:","service":"focep-chatbot","timestamp":"2025-07-28 02:23:41"}
{"component":"nlp","level":"error","message":"Erreur recherche microfinance sur Cameroon Tribune:","service":"focep-chatbot","timestamp":"2025-07-28 02:23:42"}
{"component":"nlp","level":"error","message":"Erreur recherche banque sur Cameroon Tribune:","service":"focep-chatbot","timestamp":"2025-07-28 02:23:42"}
{"component":"nlp","level":"error","message":"Erreur recherche crédit sur Cameroon Tribune:","service":"focep-chatbot","timestamp":"2025-07-28 02:23:42"}
{"component":"nlp","level":"error","message":"Erreur recherche focep sur Journal du Cameroun:","service":"focep-chatbot","timestamp":"2025-07-28 02:23:46"}
{"component":"nlp","level":"error","message":"Erreur recherche microfinance sur Journal du Cameroun:","service":"focep-chatbot","timestamp":"2025-07-28 02:23:47"}
{"component":"nlp","level":"error","message":"Erreur recherche focep sur Investir au Cameroun:","service":"focep-chatbot","timestamp":"2025-07-28 02:23:51"}
{"component":"nlp","level":"error","message":"Erreur recherche microfinance sur Investir au Cameroun:","service":"focep-chatbot","timestamp":"2025-07-28 02:23:52"}
{"component":"nlp","level":"error","message":"Erreur recherche finance sur Investir au Cameroun:","service":"focep-chatbot","timestamp":"2025-07-28 02:23:52"}
{"component":"nlp","level":"error","message":"Erreur recherche cameroun sur Financial Afrik:","service":"focep-chatbot","timestamp":"2025-07-28 02:23:58"}
{"component":"nlp","level":"error","message":"Erreur recherche microfinance sur Financial Afrik:","service":"focep-chatbot","timestamp":"2025-07-28 02:24:01"}
{"component":"nlp","level":"error","message":"Erreur recherche focep sur Financial Afrik:","service":"focep-chatbot","timestamp":"2025-07-28 02:24:04"}
{"component":"nlp","level":"error","message":"Erreur recherche focep sur Ecomnews:","service":"focep-chatbot","timestamp":"2025-07-28 02:24:07"}
{"component":"nlp","level":"error","message":"Erreur recherche microfinance sur Ecomnews:","service":"focep-chatbot","timestamp":"2025-07-28 02:24:07"}
{"component":"nlp","level":"error","message":"Erreur recherche cameroun sur Ecomnews:","service":"focep-chatbot","timestamp":"2025-07-28 02:24:07"}
{"level":"error","message":"Database connection error: cronJobs.startAll is not a function","service":"focep-chatbot","stack":"TypeError: cronJobs.startAll is not a function\n    at startServer (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\server.js:121:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 02:47:54"}
{"level":"error","message":"Erreur webhook chatbot: MessageProcessor is not a constructor","service":"focep-chatbot","stack":"TypeError: MessageProcessor is not a constructor\n    at C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\server.js:136:30\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at serveStatic (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\serve-static\\index.js:75:16)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-28 03:43:06"}
{"level":"error","message":"Erreur webhook chatbot: MessageProcessor is not a constructor","service":"focep-chatbot","stack":"TypeError: MessageProcessor is not a constructor\n    at C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\server.js:136:30\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at serveStatic (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\serve-static\\index.js:75:16)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-28 03:45:06"}
{"level":"error","message":"Erreur webhook chatbot: MessageProcessor is not a constructor","service":"focep-chatbot","stack":"TypeError: MessageProcessor is not a constructor\n    at C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\server.js:136:30\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at serveStatic (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\serve-static\\index.js:75:16)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-28 03:45:06"}
{"level":"error","message":"Erreur webhook chatbot: MessageProcessor is not a constructor","service":"focep-chatbot","stack":"TypeError: MessageProcessor is not a constructor\n    at C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\server.js:136:30\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at serveStatic (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\serve-static\\index.js:75:16)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-28 03:48:20"}
{"level":"error","message":"Erreur webhook chatbot: MessageProcessor is not a constructor","service":"focep-chatbot","stack":"TypeError: MessageProcessor is not a constructor\n    at C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\server.js:136:30\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at serveStatic (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\serve-static\\index.js:75:16)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-28 03:48:20"}
{"level":"error","message":"Erreur webhook chatbot: MessageProcessor is not a constructor","service":"focep-chatbot","stack":"TypeError: MessageProcessor is not a constructor\n    at C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\server.js:136:30\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at serveStatic (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\serve-static\\index.js:75:16)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-28 03:48:35"}
{"level":"error","message":"Erreur webhook chatbot: MessageProcessor is not a constructor","service":"focep-chatbot","stack":"TypeError: MessageProcessor is not a constructor\n    at C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\server.js:136:30\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at serveStatic (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\serve-static\\index.js:75:16)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-28 03:48:35"}
{"details":{"originalData":{"contactInfo":{"profile":{"name":"loçky"}},"content":{"text":"Bonjour"},"from":"+237676328974","messageId":"false_237676328974@c.us_889917846F27527FDFFAEB431A8D9FF8","timestamp":"2025-07-28T02:38:08.000Z","type":"chat"}},"isOperational":true,"level":"error","message":"Erreur lors du traitement <NAME_EMAIL>_889917846F27527FDFFAEB431A8D9FF8: Données de message invalides: Données de message invalides: Type de message invalide","name":"FocepError","service":"focep-chatbot","stack":"FocepError: Données de message invalides: Données de message invalides: Type de message invalide\n    at MessageProcessor.validateMessageData (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\messageProcessor.js:177:13)\n    at MessageProcessor.processMessage (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\messageProcessor.js:83:18)\n    at WhatsAppWebService.handleIncomingMessage (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\whatsappWebService.js:165:52)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Client.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\whatsappWebService.js:98:9)","statusCode":400,"timestamp":"2025-07-28 04:38:08","type":"VALIDATION_ERROR"}
{"level":"error","message":"Erreur lors de l'envoi du message d'erreur: Operation `customers.findOne()` buffering timed out after 10000ms","service":"focep-chatbot","stack":"MongooseError: Operation `customers.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:185:23)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-28 04:38:18"}
{"level":"error","message":"Erreur génération réponse intelligente: Cannot read properties of undefined (reading 'waitingFor')","service":"focep-chatbot","stack":"TypeError: Cannot read properties of undefined (reading 'waitingFor')\n    at IntelligentResponseGenerator.updateConversationState (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\intelligentResponseGenerator.js:407:26)\n    at IntelligentResponseGenerator.generateResponse (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\intelligentResponseGenerator.js:54:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async WhatsAppWebService.handleIncomingMessage (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\whatsappWebService.js:178:24)\n    at async Client.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\whatsappWebService.js:104:9)","timestamp":"2025-07-28 05:25:09"}
{"level":"error","message":"Erreur Groq AI:","service":"focep-chatbot","timestamp":"2025-07-28 05:33:47"}
{"level":"error","message":"Erreur Groq AI:","service":"focep-chatbot","timestamp":"2025-07-28 05:42:18"}
{"level":"error","message":"Erreur Groq AI analyse: Cannot read properties of undefined (reading 'text')","service":"focep-chatbot","timestamp":"2025-07-28 05:48:18"}
{"level":"error","message":"Erreur Groq AI analyse: Cannot read properties of undefined (reading 'text')","service":"focep-chatbot","timestamp":"2025-07-28 05:48:31"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99212, Requested 1298. Please try again in 7m20.53s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 07:47:06"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100447, Requested 1317. Please try again in 25m24.832s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 07:47:47"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100406, Requested 1446. Please try again in 26m40.588s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 07:48:23"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100326, Requested 1433. Please try again in 25m20.272s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 07:49:32"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100257, Requested 1264. Please try again in 21m54.82s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 07:50:32"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100255, Requested 1192. Please try again in 20m50.209s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 07:50:34"}
{"level":"error","message":"Erreur statistiques actualités: connect ETIMEDOUT","name":"SequelizeConnectionError","original":{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","fatal":true,"syscall":"connect"},"parent":{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","fatal":true,"syscall":"connect"},"service":"focep-chatbot","stack":"SequelizeConnectionError: connect ETIMEDOUT\n    at ConnectionManager.connect (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\sequelize\\lib\\dialects\\mysql\\connection-manager.js:102:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at listOnTimeout (node:internal/timers:549:9)\n    at process.processTimers (node:internal/timers:523:7)\n    at async ConnectionManager._connect (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:222:24)","timestamp":"2025-07-28 08:16:10"}
{"level":"error","message":"Erreur traitement message entrant: Protocol error (Runtime.callFunctionOn): Target closed.","name":"ProtocolError","originalMessage":"","service":"focep-chatbot","stack":"ProtocolError: Protocol error (Runtime.callFunctionOn): Target closed.\n    at C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Connection.js:329:24\n    at new Promise (<anonymous>)\n    at CDPSessionImpl.send (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Connection.js:325:16)\n    at ExecutionContext._ExecutionContext_evaluate (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\ExecutionContext.js:211:46)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\ExecutionContext.js:107:16)\n    at async Client.getChatById (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\whatsapp-web.js\\src\\Client.js:1057:22)\n    at async WhatsAppWebService.handleIncomingMessage (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\whatsappWebService.js:140:20)\n    at async Client.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\whatsappWebService.js:104:9)","timestamp":"2025-07-28 08:16:54"}
{"level":"error","message":"Erreur traitement message entrant: Protocol error (Runtime.callFunctionOn): Target closed.","name":"ProtocolError","originalMessage":"","service":"focep-chatbot","stack":"ProtocolError: Protocol error (Runtime.callFunctionOn): Target closed.\n    at C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Connection.js:329:24\n    at new Promise (<anonymous>)\n    at CDPSessionImpl.send (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Connection.js:325:16)\n    at ExecutionContext._ExecutionContext_evaluate (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\ExecutionContext.js:211:46)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\ExecutionContext.js:107:16)\n    at async Client.getChatById (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\whatsapp-web.js\\src\\Client.js:1057:22)\n    at async WhatsAppWebService.handleIncomingMessage (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\whatsappWebService.js:140:20)\n    at async Client.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\whatsappWebService.js:104:9)","timestamp":"2025-07-28 08:16:54"}
{"level":"error","message":"Erreur traitement message entrant: Protocol error (Runtime.callFunctionOn): Target closed.","name":"ProtocolError","originalMessage":"","service":"focep-chatbot","stack":"ProtocolError: Protocol error (Runtime.callFunctionOn): Target closed.\n    at C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Connection.js:329:24\n    at new Promise (<anonymous>)\n    at CDPSessionImpl.send (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Connection.js:325:16)\n    at ExecutionContext._ExecutionContext_evaluate (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\ExecutionContext.js:211:46)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\ExecutionContext.js:107:16)\n    at async Client.getChatById (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\whatsapp-web.js\\src\\Client.js:1057:22)\n    at async WhatsAppWebService.handleIncomingMessage (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\whatsappWebService.js:140:20)\n    at async Client.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\whatsappWebService.js:104:9)","timestamp":"2025-07-28 08:16:54"}
{"level":"error","message":"Erreur envoi message d'erreur: Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.","service":"focep-chatbot","stack":"Error: Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.\n    at CDPSessionImpl.send (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Connection.js:316:35)\n    at ExecutionContext._ExecutionContext_evaluate (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\ExecutionContext.js:211:46)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\ExecutionContext.js:107:16)\n    at async Client.sendMessage (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\whatsapp-web.js\\src\\Client.js:953:25)\n    at async WhatsAppWebService.handleIncomingMessage (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\whatsappWebService.js:190:9)\n    at async Client.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\whatsappWebService.js:104:9)","timestamp":"2025-07-28 08:16:54"}
{"level":"error","message":"Erreur envoi message d'erreur: Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.","service":"focep-chatbot","stack":"Error: Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.\n    at CDPSessionImpl.send (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Connection.js:316:35)\n    at ExecutionContext._ExecutionContext_evaluate (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\ExecutionContext.js:211:46)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\ExecutionContext.js:107:16)\n    at async Client.sendMessage (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\whatsapp-web.js\\src\\Client.js:953:25)\n    at async WhatsAppWebService.handleIncomingMessage (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\whatsappWebService.js:190:9)\n    at async Client.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\whatsappWebService.js:104:9)","timestamp":"2025-07-28 08:16:55"}
{"level":"error","message":"Erreur envoi message d'erreur: Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.","service":"focep-chatbot","stack":"Error: Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.\n    at CDPSessionImpl.send (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Connection.js:316:35)\n    at ExecutionContext._ExecutionContext_evaluate (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\ExecutionContext.js:211:46)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\ExecutionContext.js:107:16)\n    at async Client.sendMessage (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\whatsapp-web.js\\src\\Client.js:953:25)\n    at async WhatsAppWebService.handleIncomingMessage (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\whatsappWebService.js:190:9)\n    at async Client.<anonymous> (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\whatsappWebService.js:104:9)","timestamp":"2025-07-28 08:16:55"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100120, Requested 724. Please try again in 12m9.762999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 10:03:57"}
{"level":"error","message":"Erreur génération OpenRouter: Request failed with status code 401","service":"focep-chatbot","status":401,"timestamp":"2025-07-28 11:30:57"}
{"level":"error","message":"Erreur génération OpenRouter: Request failed with status code 401","service":"focep-chatbot","status":401,"timestamp":"2025-07-28 11:31:01"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99089, Requested 1201. Please try again in 4m10.420999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 11:31:02"}
{"level":"error","message":"Erreur génération OpenRouter: Request failed with status code 401","service":"focep-chatbot","status":401,"timestamp":"2025-07-28 11:31:02"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99088, Requested 1263. Please try again in 5m3.171s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 11:31:02"}
{"level":"error","message":"Erreur génération OpenRouter: Request failed with status code 401","service":"focep-chatbot","status":401,"timestamp":"2025-07-28 11:31:03"}
{"level":"error","message":"Erreur génération OpenRouter: Request failed with status code 400","service":"focep-chatbot","status":400,"timestamp":"2025-07-28 11:47:36"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100053, Requested 765. Please try again in 11m47.420999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 11:47:38"}
{"level":"error","message":"Erreur génération OpenRouter: Request failed with status code 400","service":"focep-chatbot","status":400,"timestamp":"2025-07-28 11:47:39"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100051, Requested 905. Please try again in 13m46.461s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 11:47:40"}
{"level":"error","message":"Erreur génération OpenRouter: Request failed with status code 400","service":"focep-chatbot","status":400,"timestamp":"2025-07-28 11:47:41"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100049, Requested 1094. Please try again in 16m28.175999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 11:47:42"}
{"level":"error","message":"Erreur génération OpenRouter: Request failed with status code 400","service":"focep-chatbot","status":400,"timestamp":"2025-07-28 11:47:42"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100048, Requested 1156. Please try again in 17m20.364s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 11:47:43"}
{"level":"error","message":"Erreur génération OpenRouter: Request failed with status code 400","service":"focep-chatbot","status":400,"timestamp":"2025-07-28 11:47:44"}
{"level":"error","message":"Erreur génération OpenRouter: Request failed with status code 401","service":"focep-chatbot","status":401,"timestamp":"2025-07-28 11:55:19"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99966, Requested 719. Please try again in 9m51.829s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 12:01:50"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99960, Requested 867. Please try again in 11m53.882999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 12:01:56"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99951, Requested 1092. Please try again in 15m0.465999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 12:02:04"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99948, Requested 1143. Please try again in 15m41.776999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 12:02:07"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99945, Requested 620. Please try again in 8m7.376s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 12:02:10"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99942, Requested 719. Please try again in 9m31.028s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 12:02:11"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99940, Requested 864. Please try again in 11m33.992s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 12:02:14"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99413, Requested 620. Please try again in 28.185s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 12:09:49"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99379, Requested 722. Please try again in 1m26.526s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 12:10:18"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99345, Requested 869. Please try again in 3m4.329s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 12:10:48"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99314, Requested 1006. Please try again in 4m36.443s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 12:11:14"}
{"level":"error","message":"Erreur initialisation WhatsApp Web: net::ERR_NAME_NOT_RESOLVED at https://web.whatsapp.com/","service":"focep-chatbot","stack":"Error: net::ERR_NAME_NOT_RESOLVED at https://web.whatsapp.com/\n    at navigate (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Frame.js:235:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Frame.goto (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Frame.js:205:21)\n    at async CDPPage.goto (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Page.js:1083:16)\n    at async Client.initialize (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\whatsapp-web.js\\src\\Client.js:323:9)\n    at async WhatsAppWebService.initialize (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\whatsappWebService.js:55:7)","timestamp":"2025-07-28 12:39:00"}
{"level":"error","message":"Erreur initialisation WhatsApp Web: net::ERR_NAME_NOT_RESOLVED at https://web.whatsapp.com/","service":"focep-chatbot","stack":"Error: net::ERR_NAME_NOT_RESOLVED at https://web.whatsapp.com/\n    at navigate (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Frame.js:235:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Frame.goto (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Frame.js:205:21)\n    at async CDPPage.goto (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Page.js:1083:16)\n    at async Client.initialize (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\whatsapp-web.js\\src\\Client.js:323:9)\n    at async WhatsAppWebService.initialize (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\whatsappWebService.js:55:7)","timestamp":"2025-07-28 12:39:00"}
{"intent":"salutation","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 12:54:45"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99767, Requested 1031. Please try again in 11m29.199s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 12:54:48"}
{"intent":"general","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 12:54:48"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99766, Requested 1223. Please try again in 14m14.188s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 12:54:49"}
{"intent":"credit_general","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 12:54:49"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99765, Requested 1265. Please try again in 14m49.55s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 12:54:50"}
{"intent":"general","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 12:54:50"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99764, Requested 623. Please try again in 5m33.947s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 12:54:50"}
{"intent":"general","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 12:54:51"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99555, Requested 1145. Please try again in 10m4.668999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 12:57:51"}
{"intent":"salutation","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 12:57:52"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99554, Requested 1049. Please try again in 8m40.352s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 12:57:52"}
{"intent":"credit_general","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 12:57:53"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99553, Requested 1048. Please try again in 8m38.640999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 12:57:53"}
{"intent":"general","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 12:57:53"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99552, Requested 1057. Please try again in 8m45.597s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 12:57:54"}
{"intent":"credit_general","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 12:57:54"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99551, Requested 1099. Please try again in 9m21.176999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 12:57:54"}
{"intent":"general","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 12:57:55"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99550, Requested 815. Please try again in 5m14.992s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 12:57:55"}
{"intent":"general","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 12:57:56"}
{"intent":"salutation","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 13:04:55"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99932, Requested 722. Please try again in 9m24.292s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 13:04:55"}
{"intent":"credit_general","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 13:04:56"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99931, Requested 865. Please try again in 11m27.025s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 13:04:56"}
{"intent":"general","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 13:04:57"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99930, Requested 1057. Please try again in 14m11.995s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 13:04:57"}
{"intent":"credit_general","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 13:04:57"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99929, Requested 1099. Please try again in 14m47.459s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 13:04:58"}
{"intent":"general","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 13:04:58"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99928, Requested 623. Please try again in 7m55.376999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 13:04:58"}
{"intent":"general","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 13:04:59"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99675, Requested 619. Please try again in 4m13.957s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 13:08:36"}
{"intent":"general","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 13:08:37"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99674, Requested 808. Please try again in 6m55.904s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 13:08:38"}
{"intent":"general","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 13:08:38"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99426, Requested 619. Please try again in 38.771s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 13:12:12"}
{"intent":"general","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 13:12:13"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99425, Requested 808. Please try again in 3m20.616s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 13:12:13"}
{"intent":"general","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 13:12:14"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100171, Requested 660. Please try again in 11m58.584s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 13:15:27"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100171, Requested 763. Please try again in 13m27.348999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 13:15:27"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99683, Requested 619. Please try again in 4m20.145999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 13:22:30"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99682, Requested 808. Please try again in 7m2.955s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 13:22:30"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99777, Requested 722. Please try again in 7m10.871s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 13:33:32"}
{"intent":"credit_general","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 13:33:33"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99776, Requested 865. Please try again in 9m13.003999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 13:33:34"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99775, Requested 1048. Please try again in 11m50.891s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 13:33:34"}
{"intent":"credit_general","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 13:33:34"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99774, Requested 1090. Please try again in 12m26.262s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 13:33:35"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99774, Requested 623. Please try again in 5m42.555s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 13:33:35"}
{"intent":"general","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 13:33:35"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100812, Requested 763. Please try again in 22m41.34s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 13:53:11"}
{"intent":"credit_commercial","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 13:53:13"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100781, Requested 903. Please try again in 24m15.48s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 13:53:38"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100738, Requested 1011. Please try again in 25m11.486999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 13:54:16"}
{"intent":"epargne","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 13:54:16"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100709, Requested 1053. Please try again in 25m22.435s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 13:54:41"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100673, Requested 1054. Please try again in 24m52.88s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-28 13:55:11"}
{"intent":"general","level":"error","message":"Erreur génération OpenRouter: Request failed with status code 429","service":"focep-chatbot","status":429,"timestamp":"2025-07-28 13:55:12"}
{"level":"error","message":"Erreur initialisation WhatsApp Web: net::ERR_INTERNET_DISCONNECTED at https://web.whatsapp.com/","service":"focep-chatbot","stack":"Error: net::ERR_INTERNET_DISCONNECTED at https://web.whatsapp.com/\n    at navigate (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Frame.js:235:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Frame.goto (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Frame.js:205:21)\n    at async CDPPage.goto (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Page.js:1083:16)\n    at async Client.initialize (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\whatsapp-web.js\\src\\Client.js:323:9)\n    at async WhatsAppWebService.initialize (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\whatsappWebService.js:61:7)","timestamp":"2025-07-29 02:24:15"}
{"level":"error","message":"Erreur initialisation WhatsApp Web: net::ERR_INTERNET_DISCONNECTED at https://web.whatsapp.com/","service":"focep-chatbot","stack":"Error: net::ERR_INTERNET_DISCONNECTED at https://web.whatsapp.com/\n    at navigate (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Frame.js:235:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Frame.goto (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Frame.js:205:21)\n    at async CDPPage.goto (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Page.js:1083:16)\n    at async Client.initialize (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\whatsapp-web.js\\src\\Client.js:323:9)\n    at async WhatsAppWebService.initialize (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\whatsappWebService.js:61:7)","timestamp":"2025-07-29 02:24:15"}
{"level":"error","message":"Erreur stats actualités: connect ETIMEDOUT","name":"SequelizeConnectionError","original":{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","fatal":true,"syscall":"connect"},"parent":{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","fatal":true,"syscall":"connect"},"service":"focep-chatbot","stack":"SequelizeConnectionError: connect ETIMEDOUT\n    at ConnectionManager.connect (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\sequelize\\lib\\dialects\\mysql\\connection-manager.js:102:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processTimers (node:internal/timers:520:9)\n    at async ConnectionManager._connect (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:222:24)","timestamp":"2025-07-29 02:28:19"}
{"code":"ECONNABORTED","level":"error","message":"Erreur Groq AI analyse: timeout of 8000ms exceeded","service":"focep-chatbot","timestamp":"2025-07-29 02:43:15"}
{"level":"error","message":"Erreur stats actualités: connect ETIMEDOUT","name":"SequelizeConnectionError","original":{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","fatal":true,"syscall":"connect"},"parent":{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","fatal":true,"syscall":"connect"},"service":"focep-chatbot","stack":"SequelizeConnectionError: connect ETIMEDOUT\n    at ConnectionManager.connect (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\sequelize\\lib\\dialects\\mysql\\connection-manager.js:102:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager._connect (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:222:24)","timestamp":"2025-07-29 02:57:41"}
{"level":"error","message":"Erreur stats actualités: connect ETIMEDOUT","name":"SequelizeConnectionError","original":{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","fatal":true,"syscall":"connect"},"parent":{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","fatal":true,"syscall":"connect"},"service":"focep-chatbot","stack":"SequelizeConnectionError: connect ETIMEDOUT\n    at ConnectionManager.connect (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\sequelize\\lib\\dialects\\mysql\\connection-manager.js:102:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager._connect (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:222:24)","timestamp":"2025-07-29 02:58:45"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 10850, Requested 1526. Please try again in 1.878999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-29 03:00:17"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 11589, Requested 987. Please try again in 2.88s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-29 03:13:18"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 11550, Requested 951. Please try again in 2.504s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-29 03:13:18"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 11513, Requested 973. Please try again in 2.429s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-29 03:13:18"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 11476, Requested 951. Please try again in 2.134999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-29 03:13:18"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 11440, Requested 973. Please try again in 2.063999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-29 03:13:18"}
{"code":"ECONNABORTED","error":"timeout of 8000ms exceeded","level":"error","message":"Erreur Gemini AI analyse","service":"focep-chatbot","timestamp":"2025-07-30 06:18:19"}
{"code":"ECONNABORTED","error":"timeout of 8000ms exceeded","level":"error","message":"Erreur Gemini AI analyse","service":"focep-chatbot","timestamp":"2025-07-30 06:18:36"}
{"level":"error","message":"Erreur initialisation WhatsApp Web: net::ERR_INTERNET_DISCONNECTED at https://web.whatsapp.com/","service":"focep-chatbot","stack":"Error: net::ERR_INTERNET_DISCONNECTED at https://web.whatsapp.com/\n    at navigate (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Frame.js:235:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Frame.goto (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Frame.js:205:21)\n    at async CDPPage.goto (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Page.js:1083:16)\n    at async Client.initialize (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\whatsapp-web.js\\src\\Client.js:323:9)\n    at async WhatsAppWebService.initialize (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\whatsappWebService.js:61:7)","timestamp":"2025-07-30 07:46:37"}
{"level":"error","message":"Erreur initialisation WhatsApp Web: net::ERR_INTERNET_DISCONNECTED at https://web.whatsapp.com/","service":"focep-chatbot","stack":"Error: net::ERR_INTERNET_DISCONNECTED at https://web.whatsapp.com/\n    at navigate (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Frame.js:235:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Frame.goto (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Frame.js:205:21)\n    at async CDPPage.goto (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Page.js:1083:16)\n    at async Client.initialize (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\node_modules\\whatsapp-web.js\\src\\Client.js:323:9)\n    at async WhatsAppWebService.initialize (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\whatsappWebService.js:61:7)","timestamp":"2025-07-30 07:46:37"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99550, Requested 967. Please try again in 7m26.685s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 12:02:07"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99550, Requested 965. Please try again in 7m24.883999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 12:02:07"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99550, Requested 1755. Please try again in 18m47.326s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 12:02:07"}
{"error":"Request failed with status code 503","intent":"general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","status":503,"timestamp":"2025-07-30 12:02:12"}
{"error":"timeout of 8000ms exceeded","intent":"general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-07-30 12:02:15"}
{"error":"timeout of 8000ms exceeded","intent":"general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-07-30 12:02:15"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99473, Requested 1602. Please try again in 15m28.602s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 12:03:13"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99447, Requested 1446. Please try again in 12m51.444s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 12:03:36"}
{"error":"timeout of 8000ms exceeded","intent":"credit_general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-07-30 12:03:44"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99405, Requested 1329. Please try again in 10m33.404s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 12:04:13"}
{"error":"timeout of 8000ms exceeded","intent":"general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-07-30 12:04:21"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 98967, Requested 1515. Please try again in 6m56.375999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 12:10:30"}
{"error":"Request failed with status code 503","intent":"general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","status":503,"timestamp":"2025-07-30 12:10:36"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 98925, Requested 1516. Please try again in 6m20.639s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 12:11:07"}
{"error":"timeout of 8000ms exceeded","intent":"general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-07-30 12:11:15"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99647, Requested 993. Please try again in 9m12.248999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 12:36:29"}
{"error":"timeout of 15000ms exceeded","intent":"general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-07-30 12:37:18"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99345, Requested 1185. Please try again in 7m37.139999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 12:40:50"}
{"error":"timeout of 15000ms exceeded","intent":"credit_general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-07-30 12:41:37"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99265, Requested 1323. Please try again in 8m27.998999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 12:41:58"}
{"code":"ECONNABORTED","level":"error","message":"Erreur Groq AI analyse: timeout of 8000ms exceeded","service":"focep-chatbot","timestamp":"2025-07-30 12:58:33"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99493, Requested 1040. Please try again in 7m40.151999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 12:59:51"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 101021, Requested 1040. Please try again in 29m41.356s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 12:59:51"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100976, Requested 1089. Please try again in 29m44.183s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:00:31"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100947, Requested 1150. Please try again in 30m11.827s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:00:55"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100914, Requested 1159. Please try again in 29m51.274s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:01:24"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100871, Requested 1320. Please try again in 31m33.196s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:02:01"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100778, Requested 1256. Please try again in 29m18.139s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:03:21"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100734, Requested 1271. Please try again in 28m52.864999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:03:59"}
{"code":"ERR_BAD_REQUEST","error":"Request failed with status code 429","level":"error","message":"Erreur Gemini AI analyse","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:15:58"}
{"error":"Request failed with status code 429","intent":"general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","status":429,"timestamp":"2025-07-30 13:15:59"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99900, Requested 953. Please try again in 12m16.397s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:16:00"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99899, Requested 994. Please try again in 12m51.441s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:16:01"}
{"code":"ERR_BAD_REQUEST","error":"Request failed with status code 429","level":"error","message":"Erreur Gemini AI analyse","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:16:02"}
{"error":"Request failed with status code 429","intent":"general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","status":429,"timestamp":"2025-07-30 13:16:04"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99894, Requested 955. Please try again in 12m13.382s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:16:05"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99894, Requested 977. Please try again in 12m32.088s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:16:05"}
{"code":"ERR_BAD_REQUEST","error":"Request failed with status code 429","level":"error","message":"Erreur Gemini AI analyse","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:16:07"}
{"error":"Request failed with status code 429","intent":"general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","status":429,"timestamp":"2025-07-30 13:16:09"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99890, Requested 950. Please try again in 12m4.911s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:16:09"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99889, Requested 972. Please try again in 12m23.661s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:16:09"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99602, Requested 953. Please try again in 7m58.756999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:20:19"}
{"error":"Request failed with status code 429","intent":"credit_general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","status":429,"timestamp":"2025-07-30 13:20:26"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99589, Requested 1108. Please try again in 10m1.789s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:20:29"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99585, Requested 1315. Please try again in 12m57.499s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:20:32"}
{"error":"Request failed with status code 429","intent":"general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","status":429,"timestamp":"2025-07-30 13:20:44"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99570, Requested 1496. Please try again in 15m20.971s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:20:45"}
{"error":"Request failed with status code 429","intent":"general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","status":429,"timestamp":"2025-07-30 13:20:48"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100444, Requested 990. Please try again in 20m39.53s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:30:55"}
{"error":"Request failed with status code 429","intent":"credit_agricole","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","status":429,"timestamp":"2025-07-30 13:31:00"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100439, Requested 1136. Please try again in 22m40.95s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:31:01"}
{"error":"Request failed with status code 429","intent":"credit_general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","status":429,"timestamp":"2025-07-30 13:31:02"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100436, Requested 1291. Please try again in 24m52.897s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:31:03"}
{"error":"Request failed with status code 429","intent":"general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","status":429,"timestamp":"2025-07-30 13:31:04"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100434, Requested 1473. Please try again in 27m28.206s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:31:05"}
{"error":"Request failed with status code 429","intent":"credit_general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","status":429,"timestamp":"2025-07-30 13:31:06"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99496, Requested 953. Please try again in 6m27.702999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:44:36"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99494, Requested 1136. Please try again in 9m3.924s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:44:38"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99491, Requested 1332. Please try again in 11m50.819s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:44:40"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99489, Requested 1469. Please try again in 13m47.223s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:44:42"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100515, Requested 995. Please try again in 21m45.267999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:53:08"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100515, Requested 1107. Please try again in 23m21.793s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:53:08"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100515, Requested 1291. Please try again in 26m0.571s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:53:08"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100514, Requested 1422. Please try again in 27m53.538s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:53:09"}
{"level":"error","message":"Erreur génération réponse intelligente: messageText is not defined","service":"focep-chatbot","stack":"ReferenceError: messageText is not defined\n    at IntelligentResponseGenerator.generateContextualResponse (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\intelligentResponseGenerator.js:225:66)\n    at IntelligentResponseGenerator.generateResponse (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\intelligentResponseGenerator.js:163:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testCorrectionMontants (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\testCorrectionMontants.js:47:30)","timestamp":"2025-07-30 13:53:09"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100040, Requested 955. Please try again in 14m19.716s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:59:57"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100039, Requested 1107. Please try again in 16m30.69s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:59:58"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100039, Requested 1291. Please try again in 19m9.369s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:59:58"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 100038, Requested 1422. Please try again in 21m2.005s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 13:59:58"}
{"code":"ECONNABORTED","error":"timeout of 10000ms exceeded","level":"error","message":"Erreur Gemini AI analyse","service":"focep-chatbot","timestamp":"2025-07-30 14:51:51"}
{"code":"ERR_BAD_RESPONSE","error":"Request failed with status code 503","level":"error","message":"Erreur Gemini AI analyse","service":"focep-chatbot","status":503,"statusText":"Service Unavailable","timestamp":"2025-07-30 14:52:05"}
{"error":"Request failed with status code 503","intent":"general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","status":503,"timestamp":"2025-07-30 14:52:06"}
{"code":"ERR_BAD_RESPONSE","error":"Request failed with status code 503","level":"error","message":"Erreur Gemini AI analyse","service":"focep-chatbot","status":503,"statusText":"Service Unavailable","timestamp":"2025-07-30 14:52:06"}
{"error":"Request failed with status code 503","intent":"general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","status":503,"timestamp":"2025-07-30 14:52:07"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 98971, Requested 1089. Please try again in 51.635s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 14:57:41"}
{"error":"Request failed with status code 503","intent":"credit_commercial","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","status":503,"timestamp":"2025-07-30 14:57:46"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 98964, Requested 1229. Please try again in 2m46.672999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 14:57:46"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 98964, Requested 1340. Please try again in 4m22.297999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 14:57:47"}
{"error":"timeout of 5000ms exceeded","intent":"actualites","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-07-30 14:57:52"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 98957, Requested 1402. Please try again in 5m10.152999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 14:57:52"}
{"error":"Request failed with status code 503","intent":"general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","status":503,"timestamp":"2025-07-30 14:57:56"}
{"code":"ECONNABORTED","level":"error","message":"Erreur Groq AI analyse: timeout of 8000ms exceeded","service":"focep-chatbot","timestamp":"2025-07-30 15:09:41"}
{"code":"ECONNABORTED","level":"error","message":"Erreur Groq AI analyse: timeout of 8000ms exceeded","service":"focep-chatbot","timestamp":"2025-07-30 15:09:41"}
{"error":"Request failed with status code 503","intent":"epargne","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","status":503,"timestamp":"2025-07-30 15:09:43"}
{"error":"Request failed with status code 503","intent":"epargne","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","status":503,"timestamp":"2025-07-30 15:09:46"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99730, Requested 1295. Please try again in 14m44.740999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 15:10:44"}
{"error":"Request failed with status code 503","intent":"agences","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","status":503,"timestamp":"2025-07-30 15:10:48"}
{"error":"Rate limit exceeded - fallback to local","level":"error","message":"Erreur Gemini AI analyse","service":"focep-chatbot","timestamp":"2025-07-30 15:23:01"}
{"error":"Rate limit exceeded - fallback to local","intent":"salutation","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-07-30 15:23:02"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99645, Requested 1045. Please try again in 9m55.877999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-07-30 15:29:49"}
{"error":"Rate limit exceeded - fallback to local","intent":"general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-07-30 15:29:53"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 10389, Requested 2179. Please try again in 2.839s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 09:36:59"}
{"error":"Rate limit exceeded - fallback to local","intent":"general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-08-01 09:37:00"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 10099, Requested 2094. Please try again in 962ms. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 09:37:00"}
{"error":"Rate limit exceeded - fallback to local","intent":"general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-08-01 09:37:01"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 12076, Requested 1819. Please try again in 9.477s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 09:37:02"}
{"error":"Rate limit exceeded - fallback to local","intent":"credit_general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-08-01 09:37:03"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 11905, Requested 1972. Please try again in 9.381s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 09:44:13"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 11849, Requested 1858. Please try again in 8.535s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 09:44:13"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 11797, Requested 1648. Please try again in 7.221s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 09:44:14"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 11796, Requested 1888. Please try again in 8.42s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 09:50:31"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 11738, Requested 1774. Please try again in 7.559s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 09:50:32"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 11688, Requested 1556. Please try again in 6.218s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 09:50:32"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 10154, Requested 1970. Please try again in 617ms. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 10:21:21"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 10097, Requested 2059. Please try again in 777.999999ms. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 10:21:21"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 12434, Requested 1989. Please try again in 12.115s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 10:21:23"}
{"error":"this.makeRequest is not a function","level":"error","message":"Erreur analyse PERFECT Groq AI","service":"focep-chatbot","timestamp":"2025-08-01 10:30:03"}
{"error":"this.makeRequest is not a function","level":"error","message":"Erreur analyse PERFECT Groq AI","service":"focep-chatbot","timestamp":"2025-08-01 10:30:03"}
{"error":"this.makeRequest is not a function","level":"error","message":"Erreur analyse PERFECT Groq AI","service":"focep-chatbot","timestamp":"2025-08-01 10:30:03"}
{"error":"this.parseGroqResponse is not a function","level":"error","message":"Erreur analyse PERFECT Groq AI","service":"focep-chatbot","timestamp":"2025-08-01 10:38:49"}
{"error":"this.parseGroqResponse is not a function","level":"error","message":"Erreur analyse PERFECT Groq AI","service":"focep-chatbot","timestamp":"2025-08-01 10:38:51"}
{"error":"this.parseGroqResponse is not a function","level":"error","message":"Erreur analyse PERFECT Groq AI","service":"focep-chatbot","timestamp":"2025-08-01 10:38:51"}
{"code":"ENOTFOUND","level":"error","message":"Erreur Groq AI analyse: getaddrinfo ENOTFOUND api.groq.com","service":"focep-chatbot","timestamp":"2025-08-01 15:38:05"}
{"error":"getaddrinfo ENOTFOUND generativelanguage.googleapis.com","intent":"actualites","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-08-01 15:38:05"}
{"code":"ENOTFOUND","level":"error","message":"Erreur Groq AI analyse: getaddrinfo ENOTFOUND api.groq.com","service":"focep-chatbot","timestamp":"2025-08-01 15:38:05"}
{"code":"ENOTFOUND","level":"error","message":"Erreur Groq AI analyse: getaddrinfo ENOTFOUND api.groq.com","service":"focep-chatbot","timestamp":"2025-08-01 15:38:05"}
{"code":"ENOTFOUND","level":"error","message":"Erreur Groq AI analyse: getaddrinfo ENOTFOUND api.groq.com","service":"focep-chatbot","timestamp":"2025-08-01 15:38:05"}
{"error":"getaddrinfo ENOTFOUND generativelanguage.googleapis.com","intent":"general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-08-01 15:38:05"}
{"error":"Rate limit exceeded - fallback to local","intent":"credit_jeunes","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-08-01 15:56:05"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 10867, Requested 2221. Please try again in 5.436s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 16:00:10"}
{"error":"Rate limit exceeded - fallback to local","intent":"credit_jeunes","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-08-01 16:00:11"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 10569, Requested 2346. Please try again in 4.574s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 16:00:11"}
{"error":"Rate limit exceeded - fallback to local","intent":"general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-08-01 16:00:11"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 10444, Requested 2109. Please try again in 2.761s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 16:00:12"}
{"error":"Rate limit exceeded - fallback to local","intent":"actualites","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-08-01 16:00:12"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 10328, Requested 1976. Please try again in 1.517999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 16:00:12"}
{"error":"Rate limit exceeded - fallback to local","intent":"general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-08-01 16:00:13"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 10208, Requested 1990. Please try again in 988.999999ms. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 16:00:13"}
{"error":"Rate limit exceeded - fallback to local","intent":"general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-08-01 16:00:13"}
{"code":"ECONNABORTED","level":"error","message":"Erreur Groq AI analyse: timeout of 8000ms exceeded","service":"focep-chatbot","timestamp":"2025-08-01 16:08:24"}
{"code":"ENOTFOUND","level":"error","message":"Erreur Groq AI analyse: getaddrinfo ENOTFOUND api.groq.com","service":"focep-chatbot","timestamp":"2025-08-01 16:08:27"}
{"code":"ENOTFOUND","level":"error","message":"Erreur Groq AI analyse: getaddrinfo ENOTFOUND api.groq.com","service":"focep-chatbot","timestamp":"2025-08-01 16:08:28"}
{"code":"ENOTFOUND","level":"error","message":"Erreur Groq AI analyse: getaddrinfo ENOTFOUND api.groq.com","service":"focep-chatbot","timestamp":"2025-08-01 16:08:29"}
{"code":"ENOTFOUND","level":"error","message":"Erreur Groq AI analyse: getaddrinfo ENOTFOUND api.groq.com","service":"focep-chatbot","timestamp":"2025-08-01 16:08:30"}
{"code":"ENOTFOUND","level":"error","message":"Erreur Groq AI analyse: getaddrinfo ENOTFOUND api.groq.com","service":"focep-chatbot","timestamp":"2025-08-01 16:08:30"}
{"code":"ENOTFOUND","level":"error","message":"Erreur Groq AI analyse: getaddrinfo ENOTFOUND api.groq.com","service":"focep-chatbot","timestamp":"2025-08-01 16:08:30"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 10819, Requested 2192. Please try again in 5.053s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 16:15:42"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 10768, Requested 2413. Please try again in 5.901s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 16:15:42"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 10702, Requested 2291. Please try again in 4.964s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 16:15:43"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 10639, Requested 2161. Please try again in 4s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 16:15:43"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 10512, Requested 2164. Please try again in 3.377999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 16:15:44"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 10419, Requested 2162. Please try again in 2.904s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 16:15:44"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 10274, Requested 2164. Please try again in 2.187s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 16:15:45"}
{"error":"Rate limit exceeded - fallback to local","intent":"general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-08-01 16:15:46"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 98080, Requested 2238. Please try again in 4m34.518s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 16:23:23"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 98080, Requested 2498. Please try again in 8m18.876s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 16:23:23"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 98080, Requested 2314. Please try again in 5m39.665999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 16:23:23"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 97455, Requested 2555. Please try again in 7.785999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 19:09:17"}
{"level":"error","message":"Erreur génération réponse intelligente: isSimpleQuestion is not defined","service":"focep-chatbot","stack":"ReferenceError: isSimpleQuestion is not defined\n    at IntelligentResponseGenerator.generateResponse (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\intelligentResponseGenerator.js:141:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testGroqActualites (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\testGroqActualites.js:41:30)","timestamp":"2025-08-01 19:09:17"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 97453, Requested 2552. Please try again in 4.201s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 19:09:18"}
{"level":"error","message":"Erreur génération réponse intelligente: isSimpleQuestion is not defined","service":"focep-chatbot","stack":"ReferenceError: isSimpleQuestion is not defined\n    at IntelligentResponseGenerator.generateResponse (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\intelligentResponseGenerator.js:141:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testGroqActualites (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\testGroqActualites.js:41:30)","timestamp":"2025-08-01 19:09:18"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 97452, Requested 2556. Please try again in 6.647999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 19:09:19"}
{"level":"error","message":"Erreur génération réponse intelligente: isSimpleQuestion is not defined","service":"focep-chatbot","stack":"ReferenceError: isSimpleQuestion is not defined\n    at IntelligentResponseGenerator.generateResponse (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\services\\intelligentResponseGenerator.js:141:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testGroqActualites (C:\\Users\\<USER>\\Desktop\\Nouveau dossier\\scripts\\testGroqActualites.js:41:30)","timestamp":"2025-08-01 19:09:19"}
{"error":"aiResponse.toLowerCase is not a function","level":"error","message":"Erreur analyse PERFECT Groq AI","service":"focep-chatbot","timestamp":"2025-08-01 19:23:38"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 98979, Requested 1594. Please try again in 8m14.745s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 20:00:17"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 98979, Requested 1849. Please try again in 11m54.782s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 20:00:17"}
{"error":"Rate limit exceeded - fallback to local","intent":"credit_general","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-08-01 20:00:18"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 98977, Requested 1999. Please try again in 14m3.252s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-01 20:00:18"}
{"attempt":1,"error":"Aucune clé Groq disponible","level":"error","message":"Erreur Groq AI","service":"groq-ai","timestamp":"2025-08-01 20:00:18"}
{"level":"error","message":"Erreur Groq AI analyse: Aucune clé Groq disponible","service":"focep-chatbot","timestamp":"2025-08-01 20:00:18"}
{"error":"Rate limit exceeded - fallback to local","intent":"agences","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-08-01 20:00:19"}
{"attempt":1,"error":"Aucune clé Groq disponible","level":"error","message":"Erreur Groq AI","service":"groq-ai","timestamp":"2025-08-01 20:00:19"}
{"error":"Aucune clé Groq disponible","level":"error","message":"Erreur analyse PERFECT Groq AI","service":"focep-chatbot","timestamp":"2025-08-01 20:00:19"}
{"error":"aiResponse.toLowerCase is not a function","level":"error","message":"Erreur analyse PERFECT Groq AI","service":"focep-chatbot","timestamp":"2025-08-02 02:04:55"}
{"error":"aiResponse.toLowerCase is not a function","level":"error","message":"Erreur analyse PERFECT Groq AI","service":"focep-chatbot","timestamp":"2025-08-02 02:06:15"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 12067, Requested 2367. Please try again in 12.17s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-03 21:24:52"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 11989, Requested 2501. Please try again in 12.449s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-03 21:24:52"}
{"error":"Rate limit exceeded - fallback to local","intent":"agences","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-08-03 21:24:53"}
{"error":"Request failed with status code 429","level":"error","message":"Erreur analyse PERFECT Groq AI","service":"focep-chatbot","status":429,"timestamp":"2025-08-03 21:24:54"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 12179, Requested 2303. Please try again in 12.413s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur génération Groq AI: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-03 21:34:58"}
{"code":"ERR_BAD_REQUEST","data":{"error":{"code":"rate_limit_exceeded","message":"Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01k13j2m3he8qbwacd6e45aeda` service tier `on_demand` on tokens per minute (TPM): Limit 12000, Used 12130, Requested 2453. Please try again in 12.919s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing","type":"tokens"}},"level":"error","message":"Erreur Groq AI analyse: Request failed with status code 429","service":"focep-chatbot","status":429,"statusText":"Too Many Requests","timestamp":"2025-08-03 21:34:58"}
{"error":"Rate limit exceeded - fallback to local","intent":"agences","level":"error","message":"Erreur génération Gemini AI","service":"focep-chatbot","timestamp":"2025-08-03 21:34:59"}
{"error":"Request failed with status code 429","level":"error","message":"Erreur analyse PERFECT Groq AI","service":"focep-chatbot","status":429,"timestamp":"2025-08-03 21:34:59"}
{"error":"aiResponse.toLowerCase is not a function","level":"error","message":"Erreur analyse PERFECT Groq AI","service":"focep-chatbot","timestamp":"2025-08-03 22:31:37"}
