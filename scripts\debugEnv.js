#!/usr/bin/env node

/**
 * Debug complet des variables d'environnement
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 DEBUG COMPLET DES VARIABLES D\'ENVIRONNEMENT');
console.log('=' .repeat(50));
console.log('');

// 1. Vérifier le fichier .env directement
console.log('📁 LECTURE DIRECTE DU FICHIER .env :');
try {
  const envPath = path.join(process.cwd(), '.env');
  const envContent = fs.readFileSync(envPath, 'utf8');
  
  const groqLines = envContent.split('\n').filter(line => 
    line.includes('GROQ_API_KEY') && !line.startsWith('#')
  );
  
  groqLines.forEach(line => {
    const [key, value] = line.split('=');
    if (value) {
      console.log(`   ${key}: ${value.substring(0, 20)}...`);
    }
  });
} catch (error) {
  console.log(`   ❌ Erreur lecture .env: ${error.message}`);
}

console.log('');

// 2. Nettoyer complètement le cache
console.log('🧹 NETTOYAGE CACHE DOTENV...');
Object.keys(require.cache).forEach(key => {
  if (key.includes('dotenv') || key.includes('.env')) {
    delete require.cache[key];
  }
});

// 3. Recharger dotenv avec force
console.log('🔄 RECHARGEMENT FORCÉ...');
require('dotenv').config({ 
  override: true,
  debug: true 
});

console.log('');

// 4. Vérifier les variables process.env
console.log('🔑 VARIABLES PROCESS.ENV :');
for (let i = 1; i <= 5; i++) {
  const key = `GROQ_API_KEY_${i}`;
  const value = process.env[key];
  console.log(`   ${key}: ${value ? value.substring(0, 20) + '...' : 'UNDEFINED'}`);
}

console.log('');

// 5. Test de rotation
console.log('🧪 TEST GESTIONNAIRE ROTATION :');
try {
  // Nettoyer le cache du gestionnaire
  Object.keys(require.cache).forEach(key => {
    if (key.includes('groqRotationManager')) {
      delete require.cache[key];
    }
  });
  
  const GroqRotationManager = require('../services/groqRotationManager');
  const manager = new GroqRotationManager();
  
  console.log(`   ✅ Clés détectées: ${manager.apiKeys.length}`);
  manager.apiKeys.forEach((key, index) => {
    console.log(`   Clé ${index + 1}: ${key.substring(0, 20)}...`);
  });
  
} catch (error) {
  console.log(`   ❌ Erreur gestionnaire: ${error.message}`);
}

console.log('');

// 6. Recommandations
console.log('💡 RECOMMANDATIONS :');
console.log('   1. Redémarrer complètement le terminal');
console.log('   2. Vérifier qu\'il n\'y a pas d\'autres fichiers .env');
console.log('   3. Tester avec : node scripts/testRotationGroq.js');
console.log('   4. Si problème persiste, recréer le fichier .env');
