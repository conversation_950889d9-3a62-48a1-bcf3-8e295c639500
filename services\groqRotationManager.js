const { logger } = require('../utils/logger');

/**
 * Gestionnaire de rotation des comptes Groq AI
 * Permet d'utiliser plusieurs clés API pour contourner les limites
 */
class GroqRotationManager {
  constructor() {
    // Clés API Groq multiples (à configurer dans .env)
    this.apiKeys = [
      process.env.GROQ_API_KEY_1?.trim(),
      process.env.GROQ_API_KEY_2?.trim(),
      process.env.GROQ_API_KEY_3?.trim(),
      process.env.GROQ_API_KEY_4?.trim(),
      process.env.GROQ_API_KEY_5?.trim()
    ].filter(key => {
      const isValid = key &&
                     key !== 'your_groq_api_key_here' &&
                     key !== 'your_second_groq_key_here' &&
                     key !== 'your_third_groq_key_here' &&
                     key !== 'your_fourth_groq_key_here' &&
                     key !== 'your_fifth_groq_key_here' &&
                     key.startsWith('gsk_');

      if (key && !isValid) {
        console.log(`Clé Groq invalide ignorée: ${key.substring(0, 10)}...`);
      }

      return isValid;
    });

    this.currentKeyIndex = 0;
    this.keyStats = {};
    
    // Initialiser les stats pour chaque clé
    this.apiKeys.forEach((key, index) => {
      this.keyStats[index] = {
        requests: 0,
        errors: 0,
        lastError: null,
        lastSuccess: Date.now(),
        rateLimitUntil: 0,
        dailyTokens: 0,
        lastReset: new Date().toDateString()
      };
    });

    // Debug des clés détectées
    console.log('🔑 CLÉS GROQ DÉTECTÉES :');
    console.log('   Variables d\'environnement :');
    console.log(`   GROQ_API_KEY_1: ${process.env.GROQ_API_KEY_1?.substring(0, 15)}...`);
    console.log(`   GROQ_API_KEY_2: ${process.env.GROQ_API_KEY_2?.substring(0, 15)}...`);
    console.log(`   GROQ_API_KEY_3: ${process.env.GROQ_API_KEY_3?.substring(0, 15)}...`);
    console.log(`   GROQ_API_KEY_4: ${process.env.GROQ_API_KEY_4?.substring(0, 15)}...`);
    console.log(`   GROQ_API_KEY_5: ${process.env.GROQ_API_KEY_5?.substring(0, 15)}...`);
    console.log('   Clés filtrées :');
    this.apiKeys.forEach((key, index) => {
      console.log(`   Clé ${index + 1}: ${key.substring(0, 15)}...`);
    });
    console.log('');

    logger.info('Gestionnaire rotation Groq initialisé', {
      keysCount: this.apiKeys.length,
      keys: this.apiKeys.map((key, i) => `${i+1}: ${key.substring(0, 10)}...`),
      service: 'groq-rotation'
    });
  }

  // Obtenir la clé API active
  getCurrentApiKey() {
    if (this.apiKeys.length === 0) {
      throw new Error('Aucune clé API Groq configurée');
    }

    // Vérifier si la clé actuelle est en rate limit
    const currentStats = this.keyStats[this.currentKeyIndex];
    if (currentStats.rateLimitUntil > Date.now()) {
      this.rotateToNextKey();
    }

    return {
      key: this.apiKeys[this.currentKeyIndex],
      index: this.currentKeyIndex
    };
  }

  // Rotation vers la clé suivante
  rotateToNextKey() {
    const oldIndex = this.currentKeyIndex;
    let attempts = 0;
    
    do {
      this.currentKeyIndex = (this.currentKeyIndex + 1) % this.apiKeys.length;
      attempts++;
      
      // Éviter boucle infinie
      if (attempts >= this.apiKeys.length) {
        logger.warn('Toutes les clés Groq en rate limit', {
          service: 'groq-rotation'
        });
        break;
      }
      
    } while (this.keyStats[this.currentKeyIndex].rateLimitUntil > Date.now());

    logger.info('Rotation clé Groq AI', {
      from: oldIndex,
      to: this.currentKeyIndex,
      service: 'groq-rotation'
    });
  }

  // Enregistrer une requête réussie
  recordSuccess(keyIndex, tokensUsed = 0) {
    const stats = this.keyStats[keyIndex];
    if (stats) {
      stats.requests++;
      stats.lastSuccess = Date.now();
      stats.dailyTokens += tokensUsed;
      
      // Reset quotas quotidiens
      const today = new Date().toDateString();
      if (stats.lastReset !== today) {
        stats.dailyTokens = tokensUsed;
        stats.lastReset = today;
      }
    }
  }

  // Enregistrer une erreur
  recordError(keyIndex, error) {
    const stats = this.keyStats[keyIndex];
    if (stats) {
      stats.errors++;
      stats.lastError = error.message;

      // Gérer rate limit
      if (error.message.includes('rate_limit_exceeded')) {
        // Rate limit pour 1 heure
        stats.rateLimitUntil = Date.now() + (60 * 60 * 1000);
        
        logger.warn('Clé Groq en rate limit', {
          keyIndex,
          until: new Date(stats.rateLimitUntil).toISOString(),
          service: 'groq-rotation'
        });

        // Rotation automatique
        this.rotateToNextKey();
      }
    }
  }

  // Obtenir les statistiques
  getStats() {
    return {
      totalKeys: this.apiKeys.length,
      currentKey: this.currentKeyIndex,
      stats: this.keyStats,
      summary: {
        totalRequests: Object.values(this.keyStats).reduce((sum, stat) => sum + stat.requests, 0),
        totalErrors: Object.values(this.keyStats).reduce((sum, stat) => sum + stat.errors, 0),
        keysInRateLimit: Object.values(this.keyStats).filter(stat => stat.rateLimitUntil > Date.now()).length
      }
    };
  }

  // Vérifier si au moins une clé est disponible
  hasAvailableKey() {
    return this.apiKeys.some((_, index) => 
      this.keyStats[index].rateLimitUntil <= Date.now()
    );
  }

  // Obtenir la meilleure clé disponible
  getBestAvailableKey() {
    let bestIndex = -1;
    let bestScore = -1;

    this.apiKeys.forEach((_, index) => {
      const stats = this.keyStats[index];
      
      // Ignorer les clés en rate limit
      if (stats.rateLimitUntil > Date.now()) return;

      // Score basé sur le succès et l'utilisation
      const successRate = stats.requests > 0 ? (stats.requests - stats.errors) / stats.requests : 1;
      const usageScore = 1 - (stats.dailyTokens / 100000); // Moins utilisée = mieux
      const score = successRate * 0.7 + usageScore * 0.3;

      if (score > bestScore) {
        bestScore = score;
        bestIndex = index;
      }
    });

    if (bestIndex >= 0) {
      this.currentKeyIndex = bestIndex;
      return {
        key: this.apiKeys[bestIndex],
        index: bestIndex
      };
    }

    throw new Error('Aucune clé Groq disponible');
  }

  // Reset des statistiques quotidiennes
  resetDailyStats() {
    const today = new Date().toDateString();
    Object.values(this.keyStats).forEach(stats => {
      if (stats.lastReset !== today) {
        stats.dailyTokens = 0;
        stats.lastReset = today;
      }
    });
  }
}

module.exports = GroqRotationManager;
