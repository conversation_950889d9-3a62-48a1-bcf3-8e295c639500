const axios = require('axios');
const { logger } = require('../utils/logger');
const PerfectKnowledge = require('./perfectKnowledge');
const GroqRotationManager = require('./groqRotationManager');

/**
 * Service Groq AI pour FOCEP Chatbot
 * Utilise l'API Groq pour des réponses intelligentes
 */
class GroqAI {
  constructor() {
    this.apiKey = process.env.GROQ_API_KEY;
    this.model = process.env.GROQ_MODEL || 'llama-3.3-70b-versatile';
    this.enabled = process.env.FREE_AI_ENABLED === 'true';
    this.baseURL = 'https://api.groq.com/openai/v1/chat/completions';
    this.perfectKnowledge = new PerfectKnowledge();

    // Gestionnaire de rotation des clés API
    this.rotationManager = new GroqRotationManager();

    if (!this.apiKey && this.enabled) {
      logger.warn('Groq API key manquante - IA désactivée');
      this.enabled = false;
    }
    
    // Contexte FOCEP pour l'IA
    this.focepContext = this.initializeFocepContext();
    
    logger.info(`Groq AI ${this.enabled ? 'activé' : 'désactivé'}`, {
      model: this.model,
      hasApiKey: !!this.apiKey
    });
  }

  // Initialiser le contexte FOCEP pour l'IA
  initializeFocepContext() {
    return `Tu es l'assistant virtuel officiel de FOCEP SA (Fonds Camerounais d'Épargne et de Crédit pour la Production), une institution de microfinance camerounaise de confiance depuis plus de 20 ans.

INFORMATIONS FOCEP SA :

CRÉDITS DISPONIBLES :
1. Crédit Commercial (12% par an, 6-24 mois, 500K-10M FCFA)
   - Pour activités commerciales existantes
   - Documents : ID, revenus, business plan, garanties
   - Avantages : Taux compétitif, remboursement flexible

2. Crédit Agricole (10% par an, 6-18 mois, 200K-5M FCFA)
   - Pour exploitants agricoles expérimentés
   - Documents : ID, titre foncier, plan de culture
   - Avantages : Taux préférentiel, période de grâce, conseil technique

3. Crédit Jeunes Entrepreneurs (10% par an, 12-24 mois, 500K-5M FCFA)
   - Pour jeunes 18-35 ans avec projets innovants
   - Documents : ID, diplôme, business plan
   - Avantages : Taux réduit, 6 mois de grâce, formation gratuite, mentorat

SOLUTIONS D'ÉPARGNE :
1. Compte d'Épargne Classique (3% par an, dépôt min 25K FCFA)
2. Épargne à Terme (5-7% par an, 6-24 mois, dépôt min 100K FCFA)
3. Épargne Projet (4% par an, pour objectifs spécifiques, dépôt min 50K FCFA)

AGENCES :
- Yaoundé (Siège) : Avenue Kennedy, +237 222 23 14 83
- Douala : Boulevard de la Liberté, +237 233 42 15 67
- Bafoussam : Rue Commerciale, +237 233 44 28 91
- Nouvelles agences : Garoua, Ngaoundéré, Bertoua

📱 APPLICATION MOBILE FOCEP :
• NOM : "FOCEP Mobile"
• DESCRIPTION : Application mobile officielle de FOCEP SA pour gérer vos services bancaires
• FONCTIONNALITÉS PRINCIPALES :
  - Consultation de solde en temps réel
  - Historique des transactions
  - Demande de crédit en ligne
  - Virements et transferts
  - Localisation des agences
  - Contact direct avec nos conseillers
  - Notifications de transactions
• TÉLÉCHARGEMENT :
  - Android : Disponible sur Google Play Store - Recherchez "FOCEP Mobile"
  - iOS : Disponible sur App Store - Recherchez "FOCEP Mobile"
• GRATUITE : Oui, 100% gratuite
• SÉCURITÉ : Application sécurisée avec authentification biométrique
• SUPPORT APP : Pour toute assistance avec l'application, contactez le +237 222 23 14 83

ACTUALITÉS RÉCENTES VÉRIFIÉES (Août 2025) :
- **31 juillet 2025** : Deux nouveaux Directeurs régionaux nommés pour booster la production commerciale
- **31 juillet 2025** : Partenariat stratégique signé entre FOCEP SA et le bureau du PNUD au Cameroun
- **31 juillet 2025** : Rencontre importante entre Proparco et FOCEP pour nouveaux financements
- **2025** : FOCEP SA confirme 17 ans d'expansion avec 15 agences et près de 300 employés
- **IMPORTANT** : L'application mobile FOCEP n'est actuellement PAS DISPONIBLE sur les stores
- **Contact WhatsApp officiel** : +237 6 50 10 34 64 (vérifié sur LinkedIn FOCEP SA)

🔧 PERFECT VISION - LOGICIEL DE MICROFINANCE FOCEP SA :
FOCEP SA utilise PERFECT VISION de CAGECFI (Togo) pour toutes ses opérations bancaires.

**MODULES PERFECT utilisés par FOCEP :**
• **Adhésion** : Gestion clientèle, parts sociales, enregistrement
• **Épargne** : DAV, DAT, plans épargne, calcul intérêts
• **Crédit** : Demandes, validation, amortissement, suivi
• **Caisse** : Opérations quotidiennes, arrêtés de caisse
• **Comptabilité** : Grand livre, balance, états financiers
• **SMS Banking** : Notifications automatiques clients
• **Mobile Banking** : Services digitaux

**PROBLÈMES PERFECT FRÉQUENTS ET SOLUTIONS :**

🔌 **Connexion PERFECT impossible :**
- Causes : Serveur arrêté, réseau, base inaccessible, licence expirée
- Solutions : Redémarrer service PERFECT, vérifier réseau, contrôler licence
- Contact : CAGECFI +228 22 26 84 61

⏳ **PERFECT très lent :**
- Causes : Base volumineuse, manque RAM, disque saturé
- Solutions : Archiver données, augmenter RAM, libérer espace

💰 **Caisse non équilibrée :**
- Procédure : Menu Caisse → Arrêté → Vérifier opérations → Corriger erreurs

💾 **Problème sauvegarde :**
- Causes : Espace insuffisant, permissions, service arrêté
- Solutions : Libérer espace, vérifier permissions, redémarrer service

📄 **Licence expirée :**
- Vérification : Administration → Licence → Date expiration
- Solution : Contacter CAGECFI immédiatement pour renouvellement

**Support PERFECT :** CAGECFI +228 22 26 84 61, <EMAIL>

INSTRUCTIONS SPÉCIALES ACTUALITÉS :
- Pour "actualités", "nouvelles", "news", "informations récentes", "dernières nouvelles" :
  * Utilise UNIQUEMENT les actualités vérifiées ci-dessus (juillet 2025)
  * Mentionne TOUJOURS : directeurs régionaux, PNUD, Proparco
  * Précise que l'application mobile n'est PAS disponible
  * Donne le WhatsApp officiel : +237 6 50 10 34 64
  * Format : 📰 **ACTUALITÉS FOCEP SA - Août 2025**

INSTRUCTIONS IMPORTANTES :
- Réponds en français avec un ton professionnel mais chaleureux
- Utilise des emojis appropriés (🏦💳💰📱🎯)
- Sois précis sur les taux, montants et conditions
- Pour ACTUALITÉS : Utilise UNIQUEMENT les informations vérifiées ci-dessus
- Oriente vers les agences pour les demandes concrètes
- Adapte tes réponses au contexte de la conversation

RÈGLES DE DÉTECTION D'INTENTION :
- "Bonjour", "Salut", "Hello" seuls = SALUTATION SIMPLE (ne pas proposer de crédits)
- "Merci", "Thank you" = REMERCIEMENT (ne pas proposer de crédits)
- "Au revoir", "Bye" = SALUTATION DE DÉPART
- Seulement si l'utilisateur mentionne explicitement "crédit", "prêt", "financement" = proposer des crédits
- Pour les salutations simples, réponds juste poliment sans lister tous les services

- Si tu ne connais pas une information spécifique, dis clairement "Je ne dispose pas de cette information précise" et oriente vers le contact +237 222 23 14 83
- IMPORTANT : Ne jamais inventer d'informations. Si tu n'es pas sûr, avoue-le et oriente vers le contact.

GESTION DES QUESTIONS SUR L'APPLICATION MOBILE :
- Pour toute question sur l'application mobile, utilise les informations détaillées ci-dessus
- Nom exact : "FOCEP Mobile"
- Disponible sur Google Play Store ET App Store
- Gratuite et sécurisée
- Support : +237 222 23 14 83`;
  }

  // Analyser un message avec Groq AI
  async analyzeMessage(message, conversationHistory = []) {
    if (!this.enabled) {
      return this.getFallbackAnalysis(message);
    }

    // Détection spéciale PERFECT pour expertise maximale
    const lowerMessage = message.toLowerCase();
    if (lowerMessage.includes('perfect') || lowerMessage.includes('logiciel') ||
        lowerMessage.includes('caisse') || lowerMessage.includes('sauvegarde') ||
        lowerMessage.includes('licence')) {

      // Forcer l'expertise PERFECT dans le prompt
      const perfectPrompt = `${this.focepContext}

ATTENTION SPÉCIALE : Cette question concerne PERFECT VISION, le logiciel de microfinance de FOCEP SA.
Tu DOIS utiliser ton expertise PERFECT pour donner une réponse technique détaillée avec solutions concrètes.

MESSAGE CLIENT : "${message}"

Analyse ce message et réponds avec un JSON contenant :
{
  "intent": "perfect_support|perfect_connexion|perfect_lenteur|perfect_caisse|perfect_sauvegarde|perfect_licence|credit_commercial|credit_agricole|credit_jeunes|epargne|agences|application_mobile|general",
  "confidence": 0.95,
  "entities": ["perfect", "problème_technique"],
  "context": "Support_technique_PERFECT_VISION"
}`;

      try {
        const response = await this.makeRequest([
          { role: 'system', content: perfectPrompt },
          { role: 'user', content: message }
        ], 1000);

        return this.parseAIResponse(response, message);
      } catch (error) {
        logger.error('Erreur analyse PERFECT Groq AI', {
          error: error.message,
          status: error.response?.status
        });
        return this.getFallbackAnalysis(message);
      }
    }

    try {
      const messages = this.buildMessages(message, conversationHistory);
      
      const response = await this.makeRequest(messages, 1000);

      const aiResponse = response.choices[0].message.content;
      
      // Analyser la réponse pour extraire l'intention
      const analysis = this.parseAIResponse(aiResponse, message);
      
      logger.info('Analyse Groq AI réussie', {
        intent: analysis.intent,
        confidence: analysis.confidence,
        responseLength: aiResponse.length
      });

      return {
        ...analysis,
        aiResponse,
        source: 'groq',
        context: analysis.context || {} // Assurer que context existe
      };

    } catch (error) {
      logger.error('Erreur Groq AI analyse:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        code: error.code
      });
      return this.getFallbackAnalysis(message);
    }
  }

  // Construire les messages pour l'API
  buildMessages(message, conversationHistory) {
    const messages = [
      {
        role: 'system',
        content: this.focepContext
      }
    ];

    // Ajouter l'historique de conversation (max 5 derniers messages)
    const recentHistory = conversationHistory.slice(-5);
    recentHistory.forEach(msg => {
      // Sécuriser l'accès au contenu du message
      let content = '';

      if (typeof msg === 'string') {
        content = msg;
      } else if (msg && msg.content) {
        if (typeof msg.content === 'string') {
          content = msg.content;
        } else if (msg.content.text) {
          content = msg.content.text;
        }
      } else if (msg && msg.text) {
        content = msg.text;
      }

      // Ajouter seulement si on a du contenu valide
      if (content && content.trim()) {
        messages.push({
          role: msg.sender === 'customer' ? 'user' : 'assistant',
          content: content
        });
      }
    });

    // Ajouter le message actuel
    messages.push({
      role: 'user',
      content: message
    });

    return messages;
  }

  // Parser la réponse de l'IA pour extraire l'intention
  parseAIResponse(aiResponse, originalMessage) {
    // Sécuriser le type de aiResponse
    const responseText = typeof aiResponse === 'string' ? aiResponse :
                        aiResponse?.choices?.[0]?.message?.content ||
                        aiResponse?.content ||
                        JSON.stringify(aiResponse);

    const lowerResponse = responseText.toLowerCase();
    const lowerMessage = originalMessage.toLowerCase().trim();

    let intent = 'general';
    let confidence = 0.8;

    // PRIORITÉ 1: Détecter les salutations simples d'abord (très important)
    if (lowerMessage === 'bonjour' || lowerMessage === 'salut' || lowerMessage === 'hello' || lowerMessage === 'bonsoir') {
      intent = 'salutation';
      confidence = 0.95;
    } else if (lowerMessage === 'merci' || lowerMessage === 'thank you' || lowerMessage === 'thanks') {
      intent = 'remerciement';
      confidence = 0.95;
    } else if (lowerMessage === 'au revoir' || lowerMessage === 'bye' || lowerMessage === 'à bientôt') {
      intent = 'salutation';
      confidence = 0.95;
    }
    // PRIORITÉ 2: Détecter les intentions métier seulement si pas de salutation
    else if (lowerResponse.includes('crédit') || lowerResponse.includes('prêt') || lowerMessage.includes('crédit') || lowerMessage.includes('prêt')) {
      if (lowerResponse.includes('commercial') || lowerMessage.includes('commercial')) intent = 'credit_commercial';
      else if (lowerResponse.includes('agricole') || lowerMessage.includes('agricole')) intent = 'credit_agricole';
      else if (lowerResponse.includes('jeune') || lowerMessage.includes('jeune')) intent = 'credit_jeunes';
      else intent = 'credit_general';
    } else if (lowerResponse.includes('épargne') || lowerResponse.includes('compte') || lowerMessage.includes('épargne')) {
      intent = 'epargne';
    } else if (lowerResponse.includes('agence') || lowerResponse.includes('adresse') || lowerMessage.includes('agence')) {
      intent = 'agences';
    } else if (lowerResponse.includes('actualité') || lowerResponse.includes('nouvelle') || lowerMessage.includes('actualité')) {
      intent = 'actualites';
    }

    return {
      intent,
      confidence,
      language: 'fr',
      sentiment: this.detectSentiment(originalMessage),
      entities: this.extractEntities(originalMessage),
      context: {
        isFollowUp: false,
        lastIntent: null,
        waitingFor: null,
        selectedProduct: null
      }
    };
  }

  // Détecter le sentiment
  detectSentiment(message) {
    const positiveWords = ['merci', 'excellent', 'parfait', 'super', 'génial'];
    const negativeWords = ['problème', 'erreur', 'mauvais', 'déçu', 'insatisfait'];
    
    const lowerMessage = message.toLowerCase();
    
    if (positiveWords.some(word => lowerMessage.includes(word))) {
      return 'positive';
    } else if (negativeWords.some(word => lowerMessage.includes(word))) {
      return 'negative';
    }
    
    return 'neutral';
  }

  // Extraire les entités
  extractEntities(message) {
    const entities = [];
    
    // Montants
    const montantMatch = message.match(/(\d+(?:\s*\d+)*)\s*(fcfa|franc|million|mille)?/i);
    if (montantMatch) {
      entities.push({
        type: 'montant',
        value: montantMatch[1].replace(/\s/g, ''),
        unit: montantMatch[2] || 'fcfa'
      });
    }
    
    // Durée
    const dureeMatch = message.match(/(\d+)\s*(mois|an|année)/i);
    if (dureeMatch) {
      entities.push({
        type: 'duree',
        value: dureeMatch[1],
        unit: dureeMatch[2]
      });
    }
    
    return entities;
  }

  // Générer une réponse avec Groq AI
  async generateResponse(message, conversationHistory = [], intent = null) {
    if (!this.enabled) {
      return null;
    }

    // Génération spécialisée PERFECT
    const lowerMessage = message.toLowerCase();
    const isPerfectQuery = lowerMessage.includes('perfect') || lowerMessage.includes('logiciel') ||
                          lowerMessage.includes('caisse') || lowerMessage.includes('sauvegarde') ||
                          lowerMessage.includes('licence') || (intent && intent.includes('perfect'));

    if (isPerfectQuery) {
      const perfectPrompt = `${this.focepContext}

EXPERTISE PERFECT REQUISE : Tu es maintenant en mode EXPERT PERFECT VISION.
Le client a un problème technique avec le logiciel PERFECT utilisé par FOCEP SA.

MESSAGE CLIENT : "${message}"
INTENTION : ${intent || 'support_perfect'}

INSTRUCTIONS SPÉCIALES PERFECT :
1. Commence par "🔧 **Support PERFECT VISION - FOCEP SA**"
2. Identifie le problème technique précis
3. Donne les solutions étape par étape
4. Mentionne CAGECFI pour support avancé (+228 22 26 84 61)
5. Utilise ton expertise technique complète
6. Sois très détaillé et professionnel

Génère une réponse d'expert technique PERFECT avec solutions concrètes :`;

      try {
        const response = await this.makeRequest([
          { role: 'system', content: perfectPrompt },
          { role: 'user', content: message }
        ], this.maxTokens || 2000);

        if (response?.choices?.[0]?.message?.content) {
          const content = response.choices[0].message.content.trim();
          logger.info('Réponse PERFECT Groq AI générée', {
            intent: intent || 'perfect_support',
            responseLength: content.length,
            tokensUsed: response.usage?.total_tokens || 0
          });
          return {
            type: 'text',
            content: {
              text: content
            },
            source: 'groq'
          };
        }
      } catch (error) {
        logger.error('Erreur génération PERFECT Groq AI', {
          error: error.message,
          intent: intent || 'perfect_support',
          status: error.response?.status
        });
        return null;
      }
    }

    try {
      const messages = this.buildMessages(message, conversationHistory);
      
      // Ajouter une instruction spécifique selon l'intention
      if (intent) {
        messages.push({
          role: 'system',
          content: this.getIntentInstruction(intent)
        });
      }

      const response = await this.makeRequest(messages, 800);

      const aiResponse = response.choices[0].message.content;
      
      logger.info('Réponse Groq AI générée', {
        intent,
        responseLength: aiResponse.length,
        tokensUsed: response.usage?.total_tokens || 0
      });

      return {
        type: 'text',
        content: {
          text: aiResponse
        },
        source: 'groq',
        confidence: 0.9
      };

    } catch (error) {
      logger.error('Erreur génération Groq AI:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        code: error.code
      });
      return null;
    }
  }

  // Instructions spécifiques par intention
  getIntentInstruction(intent) {
    const instructions = {
      credit_general: "L'utilisateur s'intéresse aux crédits. Présente les 3 types de crédits FOCEP avec leurs avantages principaux et demande quel type l'intéresse.",
      credit_commercial: "Donne les détails complets du crédit commercial FOCEP : taux 12%, durée 6-24 mois, montant 500K-10M FCFA, conditions et documents requis.",
      credit_agricole: "Donne les détails complets du crédit agricole FOCEP : taux 10%, durée 6-18 mois, montant 200K-5M FCFA, avec période de grâce.",
      credit_jeunes: "Donne les détails complets du crédit jeunes entrepreneurs FOCEP : taux 10%, durée 12-24 mois, pour 18-35 ans, avec formation gratuite.",
      epargne: "Présente les 3 solutions d'épargne FOCEP avec leurs taux et avantages, et demande laquelle intéresse l'utilisateur.",
      agences: "Donne la liste complète des agences FOCEP avec adresses, téléphones et horaires.",
      actualites: "Présente les dernières actualités FOCEP de manière engageante."
    };
    
    return instructions[intent] || "Réponds de manière utile et professionnelle selon le contexte FOCEP.";
  }

  // Analyse de fallback si Groq n'est pas disponible
  getFallbackAnalysis(message) {
    const lowerMessage = message.toLowerCase();
    
    let intent = 'general';
    if (lowerMessage.includes('crédit') || lowerMessage.includes('prêt')) {
      intent = 'credit_general';
    } else if (lowerMessage.includes('épargne')) {
      intent = 'epargne';
    } else if (lowerMessage.includes('agence')) {
      intent = 'agences';
    } else if (lowerMessage.includes('actualité')) {
      intent = 'actualites';
    } else if (lowerMessage.includes('bonjour') || lowerMessage.includes('salut')) {
      intent = 'salutation';
    }

    return {
      intent,
      confidence: 0.6,
      language: 'fr',
      sentiment: 'neutral',
      entities: [],
      source: 'fallback',
      context: {
        isFollowUp: false,
        lastIntent: null,
        waitingFor: null,
        selectedProduct: null
      }
    };
  }

  // Faire une requête à l'API Groq avec rotation automatique
  async makeRequest(messages, maxTokens = 2000) {
    let lastError = null;
    let attempts = 0;
    const maxAttempts = this.rotationManager.apiKeys.length || 1;

    while (attempts < maxAttempts) {
      try {
        // Obtenir la meilleure clé disponible
        const { key: currentApiKey, index: keyIndex } = this.rotationManager.getBestAvailableKey();

        const response = await axios.post(this.baseURL, {
          model: this.model,
          messages: messages,
          max_tokens: maxTokens,
          temperature: 0.7,
          stream: false
        }, {
          headers: {
            'Authorization': `Bearer ${currentApiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        });

        // Enregistrer le succès
        this.rotationManager.recordSuccess(keyIndex, response.data.usage?.total_tokens || 0);

        logger.info('Requête Groq réussie avec rotation', {
          keyIndex,
          tokensUsed: response.data.usage?.total_tokens || 0,
          service: 'groq-ai'
        });

        return response.data;

      } catch (error) {
        attempts++;
        lastError = error;

        if (error.response?.status === 429) {
          // Rate limit - enregistrer et passer à la clé suivante
          const currentKeyInfo = this.rotationManager.getCurrentApiKey();
          this.rotationManager.recordError(currentKeyInfo.index, error);

          logger.warn('Rate limit Groq - rotation vers clé suivante', {
            keyIndex: currentKeyInfo.index,
            attempt: attempts,
            service: 'groq-ai'
          });

          continue;
        } else {
          // Autre erreur
          logger.error('Erreur Groq AI', {
            error: error.message,
            attempt: attempts,
            service: 'groq-ai'
          });

          if (attempts < maxAttempts) {
            this.rotationManager.rotateToNextKey();
            continue;
          }
        }
      }
    }

    // Si toutes les tentatives ont échoué
    throw lastError || new Error('Toutes les clés Groq AI indisponibles');
  }

  // Vérifier le statut de l'API
  async checkStatus() {
    if (!this.enabled) {
      return { status: 'disabled', reason: 'API key manquante ou désactivée' };
    }

    try {
      const response = await axios.post(this.baseURL, {
        model: this.model,
        messages: [{ role: 'user', content: 'Test' }],
        max_tokens: 10
      }, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 5000
      });

      return { 
        status: 'active', 
        model: this.model,
        tokensUsed: response.data.usage?.total_tokens || 0
      };

    } catch (error) {
      return { 
        status: 'error', 
        error: error.message 
      };
    }
  }
}

module.exports = GroqAI;
